import numpy as np
import os

def check_and_fix_labels(dataset_name):
    """检查并修复标签文件的格式问题"""
    
    print(f"\n=== 检查数据集: {dataset_name} ===")
    
    # 文件路径
    base_path = f'src/dataset/{dataset_name}/{dataset_name}'
    test_labels_path = f'{base_path}_test_label.npy'
    
    if not os.path.exists(test_labels_path):
        print(f"标签文件不存在: {test_labels_path}")
        return
    
    # 加载原始标签
    try:
        original_labels = np.load(test_labels_path, allow_pickle=True)
        print(f"原始标签形状: {original_labels.shape}")
        print(f"原始标签数据类型: {original_labels.dtype}")
        print(f"前10个标签: {original_labels[:10]}")
        print(f"唯一标签: {np.unique(original_labels)}")
        
        # 检查标签类型
        if len(original_labels) > 0:
            first_label = original_labels[0]
            print(f"第一个标签的Python类型: {type(first_label)}")
            
            # 转换为整数
            if isinstance(first_label, (str, bytes)):
                print("检测到字符串标签，进行转换...")
                # 字符串标签转换
                if isinstance(first_label, bytes):
                    # 处理bytes类型
                    string_labels = [label.decode('utf-8') if isinstance(label, bytes) else str(label) 
                                   for label in original_labels]
                else:
                    string_labels = original_labels
                
                # 映射到0/1
                int_labels = np.array([0 if label in ['Genuine', 'Normal', 'normal', 'genuine'] else 1 
                                     for label in string_labels], dtype=np.int32)
                
            elif isinstance(first_label, (bool, np.bool_)):
                print("检测到布尔标签，转换为整数...")
                int_labels = original_labels.astype(np.int32)
                
            elif isinstance(first_label, (int, np.integer)):
                print("标签已经是整数类型")
                int_labels = original_labels.astype(np.int32)
                
            else:
                print(f"未知的标签类型: {type(first_label)}")
                # 尝试强制转换
                int_labels = np.array([1 if label else 0 for label in original_labels], dtype=np.int32)
            
            print(f"转换后标签形状: {int_labels.shape}")
            print(f"转换后标签数据类型: {int_labels.dtype}")
            print(f"转换后前10个标签: {int_labels[:10]}")
            print(f"转换后唯一标签: {np.unique(int_labels)}")
            print(f"异常标签比例: {np.mean(int_labels):.4f}")
            
            # 保存修复后的标签
            backup_path = f'{base_path}_test_label_backup.npy'
            print(f"备份原始标签到: {backup_path}")
            np.save(backup_path, original_labels)
            
            print(f"保存修复后的标签到: {test_labels_path}")
            np.save(test_labels_path, int_labels)
            
            return True
            
    except Exception as e:
        print(f"处理标签时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    # 检查所有用户数据集
    dataset_names = [
        'ConstPos', 'ConstPosOffset', 'ConstSpeed', 
        'DataReplaySybil', 'DoS', 'DoSDisruptiveSybil', 
        'DoSRandomSybil', 'GridSybil', 'RandomPos', 'RandomPosOffset'
    ]
    
    for dataset_name in dataset_names:
        try:
            check_and_fix_labels(dataset_name)
        except Exception as e:
            print(f"处理数据集 {dataset_name} 时出错: {e}")

if __name__ == "__main__":
    main() 