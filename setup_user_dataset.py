import pandas as pd
import numpy as np
import argparse
import os
from src.denoising_utils import adaptive_denoise

def denoise_data(data, window_size=5):
    """
    使用自适应去噪策略
    """
    # 转换为 numpy 并调整形状为 (1, timesteps, features) 以匹配去噪函数
    data_np = data.to_numpy().reshape(1, data.shape[0], data.shape[1])
    denoised_np = adaptive_denoise(data_np)
    # 转换回 DataFrame
    return pd.DataFrame(denoised_np[0], columns=data.columns)

def add_noise_to_data(data, noise_level=0.01):
    """
    向数据中添加高斯噪声。
    
    Args:
        data (pd.DataFrame): 输入的特征数据。
        noise_level (float): 高斯噪声的标准差。
        
    Returns:
        pd.DataFrame: 添加噪声后的数据。
    """
    noise = np.random.normal(0, noise_level, data.shape)
    return data + noise

def main(args):
    # 1. 创建输出目录
    output_dir = os.path.join('src', 'dataset', args.dataset_name)
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录已创建: {output_dir}")

    # 2. 加载数据
    try:
        df = pd.read_csv(args.input_csv, header=None)
        num_cols = df.shape[1]
        df.columns = [f'feature_{i}' for i in range(num_cols - 1)] + [args.label_column]
        print(f"成功加载数据: {args.input_csv}, 数据形状: {df.shape}")
    except FileNotFoundError:
        print(f"错误: 输入文件未找到 {args.input_csv}")
        return

    # 3. 分离标签和特征
    if args.label_column not in df.columns:
        print(f"错误: 标签列 '{args.label_column}' 在CSV中未找到。")
        return
        
    labels = df[args.label_column]
    features = df.drop(columns=[args.label_column])
    feature_names = features.columns.tolist()
    print(f"已识别 {len(feature_names)} 个特征。")

    # 4. 训练/测试数据分割
    train_size = int(len(df) * args.train_split_ratio)
    
    train_features_df = features.iloc[:train_size]
    test_features_df = features.iloc[train_size:]
    
    # train_labels = labels.iloc[:train_size] # 虽然训练标签未使用，但保持分割一致性
    test_labels = labels.iloc[train_size:]
    
    print(f"数据已分割: 训练集大小 {len(train_features_df)}, 测试集大小 {len(test_features_df)}")

    # 5. 执行去噪和增噪
    train_denoised_df = denoise_data(train_features_df, window_size=5)
    train_noisy_df = add_noise_to_data(train_features_df, noise_level=0.01)
    print("已完成去噪和增噪处理。")

    # 6. 转换为Numpy并切分成多个样本
    segment_length = 50  # 每个小样本的长度，从500缩小到50以增加样本数量
    def split_into_segments(arr, seg_len):
        n_segments = arr.shape[0] // seg_len
        if n_segments == 0:
            return arr.reshape(1, arr.shape[0], -1)  # 如果太短，保持1个样本
        segmented = arr[:n_segments * seg_len].reshape(n_segments, seg_len, -1)
        # 处理剩余部分作为最后一个样本（padding或单独）
        remainder = arr[n_segments * seg_len:]
        if len(remainder) > 0:
            padded = np.pad(remainder, ((0, seg_len - len(remainder)), (0, 0)), mode='constant', constant_values=0)
            segmented = np.vstack([segmented, padded[np.newaxis, ...]])
        return segmented

    train_features_np = split_into_segments(train_features_df.to_numpy(), segment_length)
    train_denoised_np = split_into_segments(train_denoised_df.to_numpy(), segment_length)
    train_noisy_np = split_into_segments(train_noisy_df.to_numpy(), segment_length)
    test_features_np = split_into_segments(test_features_df.to_numpy(), segment_length)

    # 测试标签切分（假设标签是per-timestep的1D数组）
    def split_labels(labels, seg_len, n_features=1):  # n_features for reshape
        labels = labels[:, np.newaxis] if labels.ndim == 1 else labels
        return split_into_segments(labels, seg_len)[:, :, 0]  # 还原1D if needed

    test_labels_np = split_labels(test_labels.to_numpy(), segment_length)

    print(f"切分后训练形状: {train_features_np.shape}, 测试形状: {test_features_np.shape}")

    # 7. 保存为 .npy 文件
    base_path = os.path.join(output_dir, args.dataset_name)
    np.save(f'{base_path}_train.npy', train_features_np)
    np.save(f'{base_path}_train_denoised.npy', train_denoised_np)
    np.save(f'{base_path}_train_noisy.npy', train_noisy_np)
    np.save(f'{base_path}_test.npy', test_features_np)
    np.save(f'{base_path}_test_label.npy', test_labels_np)
    
    print("\n所有文件已成功保存到目录:")
    print(f"  - {base_path}_train.npy (形状: {train_features_np.shape})")
    print(f"  - {base_path}_train_denoised.npy (形状: {train_denoised_np.shape})")
    print(f"  - {base_path}_train_noisy.npy (形状: {train_noisy_np.shape})")
    print(f"  - {base_path}_test.npy (形状: {test_features_np.shape})")
    print(f"  - {base_path}_test_label.npy (形状: {test_labels_np.shape})")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="为TimesURL+DECL模型预处理时间序列CSV数据。")
    parser.add_argument('--input_csv', type=str, required=True, help='输入CSV文件的路径。')
    parser.add_argument('--dataset_name', type=str, required=True, help='数据集的名称，将用于创建输出目录和文件。')
    parser.add_argument('--label_column', type=str, required=True, help='CSV文件中包含异常标签的列名。')
    parser.add_argument('--train_split_ratio', type=float, default=0.7, help='用于训练的数据比例 (例如, 0.7 表示70%)。')
    
    args = parser.parse_args()
    main(args) 