def create_decl_timesurl_architecture_diagram():
    """
    创建TimesURL+DECL协同架构图
    """
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    
    # 定义颜色
    colors = {
        'input': '#FFE4B5',
        'denoising': '#98FB98', 
        'autoregressive': '#87CEEB',
        'weight_calc': '#DDA0DD',
        'contrastive': '#F0E68C',
        'joint_opt': '#FFA07A',
        'feedback': '#FF6B6B'
    }
    
    # 1. 输入层
    input_box = patches.Rectangle((1, 8), 2, 1, 
                                 facecolor=colors['input'], 
                                 edgecolor='black', linewidth=2)
    ax.add_patch(input_box)
    ax.text(2, 8.5, 'Input\nX_orig', ha='center', va='center', fontweight='bold')
    
    # 2. 多方法去噪层
    denoising_boxes = []
    for i in range(3):
        box = patches.Rectangle((0.5 + i*1.5, 6), 1, 1,
                               facecolor=colors['denoising'],
                               edgecolor='black', linewidth=1)
        ax.add_patch(box)
        ax.text(1 + i*1.5, 6.5, f'φ_{i+1}', ha='center', va='center')
        denoising_boxes.append(box)
    
    # 3. 自回归重建模块 (核心)
    ar_box = patches.Rectangle((5, 5.5), 3, 2,
                              facecolor=colors['autoregressive'],
                              edgecolor='black', linewidth=3)
    ax.add_patch(ar_box)
    ax.text(6.5, 6.5, 'Auto-Regressive\nReconstruction\n(TSEncoder)', 
            ha='center', va='center', fontweight='bold')
    
    # 4. 权重计算模块
    weight_box = patches.Rectangle((9, 6), 2.5, 1,
                                  facecolor=colors['weight_calc'],
                                  edgecolor='black', linewidth=2)
    ax.add_patch(weight_box)
    ax.text(10.25, 6.5, 'Weight Calculation\nSoftmax(-e_j)', 
            ha='center', va='center', fontweight='bold')
    
    # 5. 对比学习模块
    cl_box = patches.Rectangle((9, 3.5), 2.5, 1.5,
                              facecolor=colors['contrastive'],
                              edgecolor='black', linewidth=2)
    ax.add_patch(cl_box)
    ax.text(10.25, 4.25, 'Weighted\nContrastive\nLearning', 
            ha='center', va='center', fontweight='bold')
    
    # 6. 联合优化
    joint_box = patches.Rectangle((5, 1), 3, 1.5,
                                 facecolor=colors['joint_opt'],
                                 edgecolor='black', linewidth=3)
    ax.add_patch(joint_box)
    ax.text(6.5, 1.75, 'Joint Optimization\nL = γL_AR + L_CL', 
            ha='center', va='center', fontweight='bold')
    
    # 添加箭头表示数据流
    # 输入到去噪
    ax.arrow(2, 8, 0, -1, head_width=0.1, head_length=0.1, fc='black', ec='black')
    
    # 去噪到自回归
    for i in range(3):
        ax.arrow(1 + i*1.5, 6, 3.5 - i*0.5, -0.3, 
                head_width=0.1, head_length=0.1, fc='blue', ec='blue')
    
    # 自回归到权重计算 (重建误差)
    ax.arrow(8, 6.5, 0.8, 0, head_width=0.1, head_length=0.1, 
            fc=colors['feedback'], ec=colors['feedback'], linewidth=3)
    ax.text(8.5, 7, 'e_j', ha='center', va='center', 
            color=colors['feedback'], fontweight='bold')
    
    # 权重到对比学习
    ax.arrow(10.25, 6, 0, -1.3, head_width=0.1, head_length=0.1, 
            fc='green', ec='green', linewidth=2)
    ax.text(10.7, 5.2, 'w_j', ha='center', va='center', 
            color='green', fontweight='bold')
    
    # 对比学习到联合优化
    ax.arrow(9.5, 3.5, -1.8, -1.8, head_width=0.1, head_length=0.1, 
            fc='purple', ec='purple', linewidth=2)
    
    # 自回归到联合优化
    ax.arrow(6.5, 5.5, 0, -2.8, head_width=0.1, head_length=0.1, 
            fc='orange', ec='orange', linewidth=2)
    
    # 反馈循环箭头 (关键!)
    ax.arrow(5, 2, -2, 2, head_width=0.1, head_length=0.1, 
            fc=colors['feedback'], ec=colors['feedback'], 
            linewidth=3, linestyle='--')
    ax.text(3, 3.5, 'Feedback\nLoop', ha='center', va='center', 
            color=colors['feedback'], fontweight='bold', fontsize=12)
    
    # 设置图形属性
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 10)
    ax.set_aspect('equal')
    ax.axis('off')
    ax.set_title('TimesURL + DECL 协同架构图\n(显示自回归与对比学习的深度耦合)', 
                fontsize=16, fontweight='bold', pad=20)
    
    # 添加图例
    legend_elements = [
        patches.Patch(color=colors['autoregressive'], label='自回归重建模块'),
        patches.Patch(color=colors['contrastive'], label='对比学习模块'),
        patches.Patch(color=colors['weight_calc'], label='权重计算'),
        patches.Patch(color=colors['feedback'], label='反馈机制')
    ]
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1, 1))
    
    plt.tight_layout()
    return fig