[{"reconstruct_target": "original", "description": "方案A: 重建原始信号 (当前方案)", "f1_score": 0.43373493975903615, "precision": 0.32727272727272727, "recall": 0.6428571428571429, "training_stable": true, "loss_info": "✅ 训练正常：0.4277 → 0.1416 (改进0.2861)", "success": true}, {"reconstruct_target": "denoised", "description": "方案B: 重建去噪信号 (审稿人建议)", "f1_score": 0.7502679528403001, "precision": 0.6003430531732419, "recall": 1.0, "training_stable": false, "loss_info": "⚠️ 训练异常：第[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]轮出现NaN损失", "success": true}]