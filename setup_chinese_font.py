#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文字体配置脚本
解决matplotlib中文显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import urllib.request

def download_chinese_font():
    """下载思源黑体字体文件"""
    font_url = "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf"
    font_dir = os.path.expanduser("~/.matplotlib/fonts")
    os.makedirs(font_dir, exist_ok=True)
    
    font_path = os.path.join(font_dir, "SourceHanSansSC-Regular.otf")
    
    if not os.path.exists(font_path):
        print("正在下载中文字体...")
        try:
            urllib.request.urlretrieve(font_url, font_path)
            print(f"字体下载完成: {font_path}")
        except Exception as e:
            print(f"字体下载失败: {e}")
            return None
    
    return font_path

def setup_matplotlib_chinese():
    """配置matplotlib中文支持"""
    # 尝试下载字体
    font_path = download_chinese_font()
    
    # 设置字体
    if font_path and os.path.exists(font_path):
        plt.rcParams['font.family'] = ['Source Han Sans SC']
    else:
        # 使用系统字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    
    plt.rcParams['axes.unicode_minus'] = False
    
    # 清除字体缓存
    fm._rebuild()
    print("中文字体配置完成!")

if __name__ == "__main__":
    setup_matplotlib_chinese()