[{"reconstruct_target": "original", "description": "方案A: 重建原始信号 (当前方案)", "f1_score": null, "precision": null, "recall": null, "success": true, "stdout": "Dataset: ConstPos\nArguments: Namespace(batch_size=8, dataset='ConstPos', epochs=30, eval=True, gpu=0, gumbel_tau=1.0, irregular=0, iters=None, lambda_align=0.1, lambda_decl=0.1, lambda_sep=0.5, lmd=0.1, load_tp=False, loader='user_anomaly', lr=0.0001, mask_ratio_per_seg=0.05, max_threads=None, max_train_length=3000, noise_amplification_factor=2.0, reconstruct_target='original', repr_dims=320, run_name='reconstruct_original_experiment', save_every=None, seed=None, segment_num=3, sgd=False, tc_timesteps=5, temp=1.0, temp_contrast=1.0)\nLoading data... done\n(480, 50, 37)\n\n🔍 === TRAINING FLOW INVESTIGATION ===\n📊 Input train_data type: <class 'dict'>\n📊 Input train_data keys: dict_keys(['x', 'x_denoised', 'x_noisy', 'mask'])\n🎯 DECL Mode Detected: True\n🔄 === DECL TRAINING BRANCH ===\n📈 Original data shape: (480, 50, 37)\n📈 Denoised data shape: (480, 50, 37)\n📈 Noisy data shape: (480, 50, 37)\n📈 Mask shape: (480, 50, 36)\n📈 Batch size: 8\n🎯 初始化Gumbel-Softmax门控网络...\n🚀 初始化基于Gumbel-Softmax的自适应去噪系统...\n✅ 自适应去噪模块已创建\n   - 输入维度: 36\n   - 去噪器数量: 7\n   - 可用去噪器: ['m_0', 'm_1', 'g_2', 'g_3', 's_4', 'm_5', 'w_6']\n✅ 门控网络初始化完成\n🌡️ 温度退火设置: 初始2.0 -> 最终0.1\n📊 DataLoader created with 60 batches\n📊 Expected batches per epoch: 60\n🔧 DECL Optimizer created with 64 parameters (包含门控网络)\n\n🎯 === TRAINING PARAMETERS INVESTIGATION ===\n📊 Input n_epochs: 30\n📊 Input n_iters: None\n📊 Final training parameters:\n   - n_epochs: 30\n   - n_iters: None\n   - max_epochs: 30\n   - batches_per_epoch: 60\n   - self.n_epochs: 0\n   - self.n_iters: 0\n\n🚀 === STARTING TRAINING LOOP ===\n\n📅 Starting Epoch 0\n🔍 Check termination: n_epochs=30, self.n_epochs=0\n🔄 === DECL TRAINING LOOP (Epoch 0) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.694 (Rec=0.829, Trip=0.655, Sep=0.427)\n🔄 Batch 40/60\n   📊 Loss: Total=0.329 (Rec=0.694, Trip=0.587, Sep=-0.154)\n   📊 Batch 40: Temp=2.000, AvgLoss=0.6334\n🔄 Batch 60/60\n   📊 Loss: Total=0.226 (Rec=0.878, Trip=0.629, Sep=-0.551)\n📊 DECL Epoch 0 completed: 60 batches processed\n\n📊 === EPOCH 0 COMPLETED ===\n   ⏱️ Epoch time: 5.234s\n   ⏱️ Total time so far: 5.234s\n   📈 Batches processed: 60\n   📈 Average loss: 0.566755\n   📈 Total iterations so far: 60\n   🔍 Interrupted flag: False\nEpoch #0: loss=0.5667554408311843\n   📈 Incremented self.n_epochs to: 1\n\n📅 Starting Epoch 1\n🔍 Check termination: n_epochs=30, self.n_epochs=1\n🔄 === DECL TRAINING LOOP (Epoch 1) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.653 (Rec=0.919, Trip=0.609, Sep=0.264)\n🔄 Batch 40/60\n   📊 Loss: Total=0.146 (Rec=0.854, Trip=0.632, Sep=-0.689)\n   📊 Batch 40: Temp=1.810, AvgLoss=0.4524\n🔄 Batch 60/60\n   📊 Loss: Total=0.178 (Rec=0.954, Trip=0.671, Sep=-0.731)\n📊 DECL Epoch 1 completed: 60 batches processed\n\n📊 === EPOCH 1 COMPLETED ===\n   ⏱️ Epoch time: 4.467s\n   ⏱️ Total time so far: 9.701s\n   📈 Batches processed: 60\n   📈 Average loss: 0.417319\n   📈 Total iterations so far: 120\n   🔍 Interrupted flag: False\nEpoch #1: loss=0.41731854851047195\n   📈 Incremented self.n_epochs to: 2\n\n📅 Starting Epoch 2\n🔍 Check termination: n_epochs=30, self.n_epochs=2\n🔄 === DECL TRAINING LOOP (Epoch 2) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.097 (Rec=0.742, Trip=0.624, Sep=-0.673)\n🔄 Batch 40/60\n   📊 Loss: Total=0.019 (Rec=0.784, Trip=0.622, Sep=-0.871)\n   📊 Batch 40: Temp=1.638, AvgLoss=0.3682\n🔄 Batch 60/60\n   📊 Loss: Total=0.229 (Rec=1.054, Trip=0.573, Sep=-0.711)\n📊 DECL Epoch 2 completed: 60 batches processed\n\n📊 === EPOCH 2 COMPLETED ===\n   ⏱️ Epoch time: 5.359s\n   ⏱️ Total time so far: 15.059s\n   📈 Batches processed: 60\n   📈 Average loss: 0.361167\n   📈 Total iterations so far: 180\n   🔍 Interrupted flag: False\nEpoch #2: loss=0.3611666222413381\n   📈 Incremented self.n_epochs to: 3\n\n📅 Starting Epoch 3\n🔍 Check termination: n_epochs=30, self.n_epochs=3\n🔄 === DECL TRAINING LOOP (Epoch 3) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.006 (Rec=0.744, Trip=0.712, Sep=-0.875)\n🔄 Batch 40/60\n   📊 Loss: Total=0.230 (Rec=1.264, Trip=0.579, Sep=-0.920)\n   📊 Batch 40: Temp=1.482, AvgLoss=0.3046\n🔄 Batch 60/60\n   📊 Loss: Total=0.125 (Rec=0.899, Trip=0.654, Sep=-0.779)\n📊 DECL Epoch 3 completed: 60 batches processed\n\n📊 === EPOCH 3 COMPLETED ===\n   ⏱️ Epoch time: 4.537s\n   ⏱️ Total time so far: 19.596s\n   📈 Batches processed: 60\n   📈 Average loss: 0.256592\n   📈 Total iterations so far: 240\n   🔍 Interrupted flag: False\nEpoch #3: loss=0.25659243141611415\n   📈 Incremented self.n_epochs to: 4\n\n📅 Starting Epoch 4\n🔍 Check termination: n_epochs=30, self.n_epochs=4\n🔄 === DECL TRAINING LOOP (Epoch 4) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.367 (Rec=0.838, Trip=0.654, Sep=-0.234)\n🔄 Batch 40/60\n   📊 Loss: Total=0.199 (Rec=0.960, Trip=0.632, Sep=-0.688)\n   📊 Batch 40: Temp=1.341, AvgLoss=0.2193\n🔄 Batch 60/60\n   📊 Loss: Total=0.617 (Rec=0.707, Trip=0.601, Sep=0.406)\n📊 DECL Epoch 4 completed: 60 batches processed\n\n📊 === EPOCH 4 COMPLETED ===\n   ⏱️ Epoch time: 4.706s\n   ⏱️ Total time so far: 24.303s\n   📈 Batches processed: 60\n   📈 Average loss: 0.233446\n   📈 Total iterations so far: 300\n   🔍 Interrupted flag: False\nEpoch #4: loss=0.23344631791114806\n   📈 Incremented self.n_epochs to: 5\n\n📅 Starting Epoch 5\n🔍 Check termination: n_epochs=30, self.n_epochs=5\n🔄 === DECL TRAINING LOOP (Epoch 5) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.848 (Rec=0.950, Trip=0.690, Sep=0.609)\n🔄 Batch 40/60\n   📊 Loss: Total=0.018 (Rec=0.862, Trip=0.644, Sep=-0.955)\n   📊 Batch 40: Temp=1.214, AvgLoss=0.3352\n🔄 Batch 60/60\n   📊 Loss: Total=0.010 (Rec=0.863, Trip=0.655, Sep=-0.974)\n📊 DECL Epoch 5 completed: 60 batches processed\n\n📊 === EPOCH 5 COMPLETED ===\n   ⏱️ Epoch time: 4.384s\n   ⏱️ Total time so far: 28.687s\n   📈 Batches processed: 60\n   📈 Average loss: 0.338818\n   📈 Total iterations so far: 360\n   🔍 Interrupted flag: False\nEpoch #5: loss=0.3388175770640373\n   📈 Incremented self.n_epochs to: 6\n\n📅 Starting Epoch 6\n🔍 Check termination: n_epochs=30, self.n_epochs=6\n🔄 === DECL TRAINING LOOP (Epoch 6) ===\n🔄 Batch 20/60\n   📊 Loss: Total=1.139 (Rec=1.248, Trip=0.606, Sep=0.909)\n🔄 Batch 40/60\n   📊 Loss: Total=-0.015 (Rec=0.768, Trip=0.585, Sep=-0.915)\n   📊 Batch 40: Temp=1.099, AvgLoss=0.2189\n🔄 Batch 60/60\n   📊 Loss: Total=0.723 (Rec=0.815, Trip=0.640, Sep=0.502)\n📊 DECL Epoch 6 completed: 60 batches processed\n\n📊 === EPOCH 6 COMPLETED ===\n   ⏱️ Epoch time: 4.431s\n   ⏱️ Total time so far: 33.118s\n   📈 Batches processed: 60\n   📈 Average loss: 0.241739\n   📈 Total iterations so far: 420\n   🔍 Interrupted flag: False\nEpoch #6: loss=0.24173940643668174\n   📈 Incremented self.n_epochs to: 7\n\n📅 Starting Epoch 7\n🔍 Check termination: n_epochs=30, self.n_epochs=7\n🔄 === DECL TRAINING LOOP (Epoch 7) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.924 (Rec=0.807, Trip=0.638, Sep=0.914)\n🔄 Batch 40/60\n   📊 Loss: Total=0.048 (Rec=0.776, Trip=0.560, Sep=-0.791)\n   📊 Batch 40: Temp=0.994, AvgLoss=0.3214\n🔄 Batch 60/60\n   📊 Loss: Total=-0.122 (Rec=0.600, Trip=0.510, Sep=-0.945)\n📊 DECL Epoch 7 completed: 60 batches processed\n\n📊 === EPOCH 7 COMPLETED ===\n   ⏱️ Epoch time: 4.569s\n   ⏱️ Total time so far: 37.687s\n   📈 Batches processed: 60\n   📈 Average loss: 0.265345\n   📈 Total iterations so far: 480\n   🔍 Interrupted flag: False\nEpoch #7: loss=0.2653447854022185\n   📈 Incremented self.n_epochs to: 8\n\n📅 Starting Epoch 8\n🔍 Check termination: n_epochs=30, self.n_epochs=8\n🔄 === DECL TRAINING LOOP (Epoch 8) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.050 (Rec=0.864, Trip=0.702, Sep=-0.905)\n🔄 Batch 40/60\n   📊 Loss: Total=0.054 (Rec=0.896, Trip=0.685, Sep=-0.926)\n   📊 Batch 40: Temp=0.900, AvgLoss=0.1604\n🔄 Batch 60/60\n   📊 Loss: Total=0.206 (Rec=0.681, Trip=0.611, Sep=-0.391)\n📊 DECL Epoch 8 completed: 60 batches processed\n\n📊 === EPOCH 8 COMPLETED ===\n   ⏱️ Epoch time: 4.230s\n   ⏱️ Total time so far: 41.917s\n   📈 Batches processed: 60\n   📈 Average loss: 0.188641\n   📈 Total iterations so far: 540\n   🔍 Interrupted flag: False\nEpoch #8: loss=0.1886412022014459\n   📈 Incremented self.n_epochs to: 9\n\n📅 Starting Epoch 9\n🔍 Check termination: n_epochs=30, self.n_epochs=9\n🔄 === DECL TRAINING LOOP (Epoch 9) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.016 (Rec=0.820, Trip=0.603, Sep=-0.907)\n🔄 Batch 40/60\n   📊 Loss: Total=0.001 (Rec=0.785, Trip=0.628, Sep=-0.908)\n   📊 Batch 40: Temp=0.814, AvgLoss=0.1538\n🔄 Batch 60/60\n   📊 Loss: Total=-0.028 (Rec=0.757, Trip=0.585, Sep=-0.931)\n📊 DECL Epoch 9 completed: 60 batches processed\n\n📊 === EPOCH 9 COMPLETED ===\n   ⏱️ Epoch time: 4.363s\n   ⏱️ Total time so far: 46.280s\n   📈 Batches processed: 60\n   📈 Average loss: 0.187993\n   📈 Total iterations so far: 600\n   🔍 Interrupted flag: False\nEpoch #9: loss=0.1879933958252271\n   📈 Incremented self.n_epochs to: 10\n\n📅 Starting Epoch 10\n🔍 Check termination: n_epochs=30, self.n_epochs=10\n🔄 === DECL TRAINING LOOP (Epoch 10) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.008 (Rec=0.824, Trip=0.631, Sep=-0.934)\n🔄 Batch 40/60\n   📊 Loss: Total=-0.094 (Rec=0.692, Trip=0.522, Sep=-0.985)\n   📊 Batch 40: Temp=0.737, AvgLoss=0.2197\n🔄 Batch 60/60\n   📊 Loss: Total=0.026 (Rec=0.896, Trip=0.686, Sep=-0.982)\n📊 DECL Epoch 10 completed: 60 batches processed\n\n📊 === EPOCH 10 COMPLETED ===\n   ⏱️ Epoch time: 4.447s\n   ⏱️ Total time so far: 50.727s\n   📈 Batches processed: 60\n   📈 Average loss: 0.194460\n   📈 Total iterations so far: 660\n   🔍 Interrupted flag: False\nEpoch #10: loss=0.19445992062489192\n   📈 Incremented self.n_epochs to: 11\n\n📅 Starting Epoch 11\n🔍 Check termination: n_epochs=30, self.n_epochs=11\n🔄 === DECL TRAINING LOOP (Epoch 11) ===\n🔄 Batch 20/60\n   📊 Loss: Total=-0.105 (Rec=0.674, Trip=0.492, Sep=-0.983)\n🔄 Batch 40/60\n   📊 Loss: Total=0.013 (Rec=0.835, Trip=0.727, Sep=-0.955)\n   📊 Batch 40: Temp=0.667, AvgLoss=0.2398\n🔄 Batch 60/60\n   📊 Loss: Total=-0.025 (Rec=0.828, Trip=0.566, Sep=-0.991)\n📊 DECL Epoch 11 completed: 60 batches processed\n\n📊 === EPOCH 11 COMPLETED ===\n   ⏱️ Epoch time: 4.294s\n   ⏱️ Total time so far: 55.022s\n   📈 Batches processed: 60\n   📈 Average loss: 0.223767\n   📈 Total iterations so far: 720\n   🔍 Interrupted flag: False\nEpoch #11: loss=0.2237669085462888\n   📈 Incremented self.n_epochs to: 12\n\n📅 Starting Epoch 12\n🔍 Check termination: n_epochs=30, self.n_epochs=12\n🔄 === DECL TRAINING LOOP (Epoch 12) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.070 (Rec=0.764, Trip=0.564, Sep=-0.737)\n🔄 Batch 40/60\n   📊 Loss: Total=-0.010 (Rec=0.840, Trip=0.599, Sep=-0.980)\n   📊 Batch 40: Temp=0.603, AvgLoss=0.2024\n🔄 Batch 60/60\n   📊 Loss: Total=-0.017 (Rec=0.817, Trip=0.541, Sep=-0.959)\n📊 DECL Epoch 12 completed: 60 batches processed\n\n📊 === EPOCH 12 COMPLETED ===\n   ⏱️ Epoch time: 4.480s\n   ⏱️ Total time so far: 59.501s\n   📈 Batches processed: 60\n   📈 Average loss: 0.195652\n   📈 Total iterations so far: 780\n   🔍 Interrupted flag: False\nEpoch #12: loss=0.1956522027651469\n   📈 Incremented self.n_epochs to: 13\n\n📅 Starting Epoch 13\n🔍 Check termination: n_epochs=30, self.n_epochs=13\n🔄 === DECL TRAINING LOOP (Epoch 13) ===\n🔄 Batch 20/60\n   📊 Loss: Total=-0.019 (Rec=0.822, Trip=0.584, Sep=-0.978)\n🔄 Batch 40/60\n   📊 Loss: Total=0.253 (Rec=1.058, Trip=0.528, Sep=-0.658)\n   📊 Batch 40: Temp=0.546, AvgLoss=0.2649\n🔄 Batch 60/60\n   📊 Loss: Total=-0.001 (Rec=0.856, Trip=0.583, Sep=-0.975)\n📊 DECL Epoch 13 completed: 60 batches processed\n\n📊 === EPOCH 13 COMPLETED ===\n   ⏱️ Epoch time: 4.593s\n   ⏱️ Total time so far: 64.095s\n   📈 Batches processed: 60\n   📈 Average loss: 0.250167\n   📈 Total iterations so far: 840\n   🔍 Interrupted flag: False\nEpoch #13: loss=0.2501670668522517\n   📈 Incremented self.n_epochs to: 14\n\n📅 Starting Epoch 14\n🔍 Check termination: n_epochs=30, self.n_epochs=14\n🔄 === DECL TRAINING LOOP (Epoch 14) ===\n🔄 Batch 20/60\n   📊 Loss: Total=-0.022 (Rec=0.828, Trip=0.586, Sep=-0.988)\n🔄 Batch 40/60\n   📊 Loss: Total=-0.027 (Rec=0.766, Trip=0.667, Sep=-0.953)\n   📊 Batch 40: Temp=0.494, AvgLoss=0.2593\n🔄 Batch 60/60\n   📊 Loss: Total=-0.054 (Rec=0.749, Trip=0.587, Sep=-0.973)\n📊 DECL Epoch 14 completed: 60 batches processed\n\n📊 === EPOCH 14 COMPLETED ===\n   ⏱️ Epoch time: 5.282s\n   ⏱️ Total time so far: 69.377s\n   📈 Batches processed: 60\n   📈 Average loss: 0.206347\n   📈 Total iterations so far: 900\n   🔍 Interrupted flag: False\nEpoch #14: loss=0.206346798936526\n   📈 Incremented self.n_epochs to: 15\n\n📅 Starting Epoch 15\n🔍 Check termination: n_epochs=30, self.n_epochs=15\n🔄 === DECL TRAINING LOOP (Epoch 15) ===\n🔄 Batch 20/60\n   📊 Loss: Total=-0.030 (Rec=0.806, Trip=0.585, Sep=-0.983)\n🔄 Batch 40/60\n   📊 Loss: Total=1.021 (Rec=0.941, Trip=0.634, Sep=0.974)\n   📊 Batch 40: Temp=0.447, AvgLoss=0.3577\n🔄 Batch 60/60\n   📊 Loss: Total=0.020 (Rec=0.906, Trip=0.600, Sep=-0.985)\n📊 DECL Epoch 15 completed: 60 batches processed\n\n📊 === EPOCH 15 COMPLETED ===\n   ⏱️ Epoch time: 5.012s\n   ⏱️ Total time so far: 74.389s\n   📈 Batches processed: 60\n   📈 Average loss: 0.307850\n   📈 Total iterations so far: 960\n   🔍 Interrupted flag: False\nEpoch #15: loss=0.30785027196009956\n   📈 Incremented self.n_epochs to: 16\n\n📅 Starting Epoch 16\n🔍 Check termination: n_epochs=30, self.n_epochs=16\n🔄 === DECL TRAINING LOOP (Epoch 16) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.123 (Rec=0.935, Trip=0.704, Sep=-0.829)\n🔄 Batch 40/60\n   📊 Loss: Total=-0.079 (Rec=0.713, Trip=0.562, Sep=-0.984)\n   📊 Batch 40: Temp=0.405, AvgLoss=0.2491\n🔄 Batch 60/60\n   📊 Loss: Total=0.023 (Rec=0.808, Trip=0.635, Sep=-0.889)\n📊 DECL Epoch 16 completed: 60 batches processed\n\n📊 === EPOCH 16 COMPLETED ===\n   ⏱️ Epoch time: 4.581s\n   ⏱️ Total time so far: 78.970s\n   📈 Batches processed: 60\n   📈 Average loss: 0.206198\n   📈 Total iterations so far: 1020\n   🔍 Interrupted flag: False\nEpoch #16: loss=0.20619765520095826\n   📈 Incremented self.n_epochs to: 17\n\n📅 Starting Epoch 17\n🔍 Check termination: n_epochs=30, self.n_epochs=17\n🔄 === DECL TRAINING LOOP (Epoch 17) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.055 (Rec=0.983, Trip=0.553, Sep=-0.983)\n🔄 Batch 40/60\n   📊 Loss: Total=0.422 (Rec=1.679, Trip=0.599, Sep=-0.956)\n   📊 Batch 40: Temp=0.366, AvgLoss=0.2650\n🔄 Batch 60/60\n   📊 Loss: Total=-0.057 (Rec=0.725, Trip=0.680, Sep=-0.974)\n📊 DECL Epoch 17 completed: 60 batches processed\n\n📊 === EPOCH 17 COMPLETED ===\n   ⏱️ Epoch time: 4.854s\n   ⏱️ Total time so far: 83.824s\n   📈 Batches processed: 60\n   📈 Average loss: 0.234562\n   📈 Total iterations so far: 1080\n   🔍 Interrupted flag: False\nEpoch #17: loss=0.23456203937530518\n   📈 Incremented self.n_epochs to: 18\n\n📅 Starting Epoch 18\n🔍 Check termination: n_epochs=30, self.n_epochs=18\n🔄 === DECL TRAINING LOOP (Epoch 18) ===\n🔄 Batch 20/60\n   📊 Loss: Total=-0.085 (Rec=0.723, Trip=0.502, Sep=-0.993)\n🔄 Batch 40/60\n   📊 Loss: Total=0.088 (Rec=1.006, Trip=0.699, Sep=-0.971)\n   📊 Batch 40: Temp=0.331, AvgLoss=0.2428\n🔄 Batch 60/60\n   📊 Loss: Total=-0.049 (Rec=0.776, Trip=0.558, Sep=-0.985)\n📊 DECL Epoch 18 completed: 60 batches processed\n\n📊 === EPOCH 18 COMPLETED ===\n   ⏱️ Epoch time: 4.639s\n   ⏱️ Total time so far: 88.462s\n   📈 Batches processed: 60\n   📈 Average loss: 0.210066\n   📈 Total iterations so far: 1140\n   🔍 Interrupted flag: False\nEpoch #18: loss=0.21006558587153754\n   📈 Incremented self.n_epochs to: 19\n\n📅 Starting Epoch 19\n🔍 Check termination: n_epochs=30, self.n_epochs=19\n🔄 === DECL TRAINING LOOP (Epoch 19) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.065 (Rec=0.888, Trip=0.632, Sep=-0.885)\n🔄 Batch 40/60\n   📊 Loss: Total=0.094 (Rec=1.047, Trip=0.654, Sep=-0.989)\n   📊 Batch 40: Temp=0.300, AvgLoss=0.2194\n🔄 Batch 60/60\n   📊 Loss: Total=0.966 (Rec=0.880, Trip=0.592, Sep=0.932)\n📊 DECL Epoch 19 completed: 60 batches processed\n\n📊 === EPOCH 19 COMPLETED ===\n   ⏱️ Epoch time: 6.734s\n   ⏱️ Total time so far: 95.197s\n   📈 Batches processed: 60\n   📈 Average loss: 0.257190\n   📈 Total iterations so far: 1200\n   🔍 Interrupted flag: False\nEpoch #19: loss=0.25719042122364044\n   📈 Incremented self.n_epochs to: 20\n\n📅 Starting Epoch 20\n🔍 Check termination: n_epochs=30, self.n_epochs=20\n🔄 === DECL TRAINING LOOP (Epoch 20) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.069 (Rec=0.983, Trip=0.680, Sep=-0.981)\n🔄 Batch 40/60\n   📊 Loss: Total=0.029 (Rec=0.903, Trip=0.623, Sep=-0.969)\n   📊 Batch 40: Temp=0.271, AvgLoss=0.2076\n🔄 Batch 60/60\n   📊 Loss: Total=-0.076 (Rec=0.728, Trip=0.558, Sep=-0.992)\n📊 DECL Epoch 20 completed: 60 batches processed\n\n📊 === EPOCH 20 COMPLETED ===\n   ⏱️ Epoch time: 4.633s\n   ⏱️ Total time so far: 99.830s\n   📈 Batches processed: 60\n   📈 Average loss: 0.220627\n   📈 Total iterations so far: 1260\n   🔍 Interrupted flag: False\nEpoch #20: loss=0.2206266090273857\n   📈 Incremented self.n_epochs to: 21\n\n📅 Starting Epoch 21\n🔍 Check termination: n_epochs=30, self.n_epochs=21\n🔄 === DECL TRAINING LOOP (Epoch 21) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.246 (Rec=0.845, Trip=0.554, Sep=-0.464)\n🔄 Batch 40/60\n   📊 Loss: Total=0.710 (Rec=0.864, Trip=0.489, Sep=0.458)\n   📊 Batch 40: Temp=0.246, AvgLoss=0.2011\n🔄 Batch 60/60\n   📊 Loss: Total=0.243 (Rec=1.320, Trip=0.586, Sep=-0.951)\n📊 DECL Epoch 21 completed: 60 batches processed\n\n📊 === EPOCH 21 COMPLETED ===\n   ⏱️ Epoch time: 4.396s\n   ⏱️ Total time so far: 104.226s\n   📈 Batches processed: 60\n   📈 Average loss: 0.206872\n   📈 Total iterations so far: 1320\n   🔍 Interrupted flag: False\nEpoch #21: loss=0.20687172412872315\n   📈 Incremented self.n_epochs to: 22\n\n📅 Starting Epoch 22\n🔍 Check termination: n_epochs=30, self.n_epochs=22\n🔄 === DECL TRAINING LOOP (Epoch 22) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.000 (Rec=0.852, Trip=0.609, Sep=-0.973)\n🔄 Batch 40/60\n   📊 Loss: Total=0.985 (Rec=0.940, Trip=0.608, Sep=0.909)\n   📊 Batch 40: Temp=0.222, AvgLoss=0.2111\n🔄 Batch 60/60\n   📊 Loss: Total=0.974 (Rec=0.852, Trip=0.649, Sep=0.965)\n📊 DECL Epoch 22 completed: 60 batches processed\n\n📊 === EPOCH 22 COMPLETED ===\n   ⏱️ Epoch time: 4.733s\n   ⏱️ Total time so far: 108.959s\n   📈 Batches processed: 60\n   📈 Average loss: 0.181470\n   📈 Total iterations so far: 1380\n   🔍 Interrupted flag: False\nEpoch #22: loss=0.1814699669679006\n   📈 Incremented self.n_epochs to: 23\n\n📅 Starting Epoch 23\n🔍 Check termination: n_epochs=30, self.n_epochs=23\n🔄 === DECL TRAINING LOOP (Epoch 23) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.990 (Rec=0.840, Trip=0.725, Sep=0.996)\n🔄 Batch 40/60\n   📊 Loss: Total=-0.034 (Rec=0.780, Trip=0.534, Sep=-0.955)\n   📊 Batch 40: Temp=0.201, AvgLoss=0.1528\n🔄 Batch 60/60\n   📊 Loss: Total=-0.067 (Rec=0.725, Trip=0.595, Sep=-0.978)\n📊 DECL Epoch 23 completed: 60 batches processed\n\n📊 === EPOCH 23 COMPLETED ===\n   ⏱️ Epoch time: 4.890s\n   ⏱️ Total time so far: 113.849s\n   📈 Batches processed: 60\n   📈 Average loss: 0.177269\n   📈 Total iterations so far: 1440\n   🔍 Interrupted flag: False\nEpoch #23: loss=0.1772689829270045\n   📈 Incremented self.n_epochs to: 24\n\n📅 Starting Epoch 24\n🔍 Check termination: n_epochs=30, self.n_epochs=24\n🔄 === DECL TRAINING LOOP (Epoch 24) ===\n🔄 Batch 20/60\n   📊 Loss: Total=-0.071 (Rec=0.738, Trip=0.515, Sep=-0.984)\n🔄 Batch 40/60\n   📊 Loss: Total=0.106 (Rec=1.112, Trip=0.460, Sep=-0.991)\n   📊 Batch 40: Temp=0.182, AvgLoss=0.1905\n🔄 Batch 60/60\n   📊 Loss: Total=0.094 (Rec=1.040, Trip=0.617, Sep=-0.975)\n📊 DECL Epoch 24 completed: 60 batches processed\n\n📊 === EPOCH 24 COMPLETED ===\n   ⏱️ Epoch time: 4.333s\n   ⏱️ Total time so far: 118.182s\n   📈 Batches processed: 60\n   📈 Average loss: 0.198457\n   📈 Total iterations so far: 1500\n   🔍 Interrupted flag: False\nEpoch #24: loss=0.19845669368902843\n   📈 Incremented self.n_epochs to: 25\n\n📅 Starting Epoch 25\n🔍 Check termination: n_epochs=30, self.n_epochs=25\n🔄 === DECL TRAINING LOOP (Epoch 25) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.022 (Rec=0.916, Trip=0.597, Sep=-0.991)\n🔄 Batch 40/60\n   📊 Loss: Total=0.047 (Rec=0.953, Trip=0.641, Sep=-0.988)\n   📊 Batch 40: Temp=0.165, AvgLoss=0.1651\n🔄 Batch 60/60\n   📊 Loss: Total=-0.062 (Rec=0.739, Trip=0.601, Sep=-0.984)\n📊 DECL Epoch 25 completed: 60 batches processed\n\n📊 === EPOCH 25 COMPLETED ===\n   ⏱️ Epoch time: 4.524s\n   ⏱️ Total time so far: 122.707s\n   📈 Batches processed: 60\n   📈 Average loss: 0.127555\n   📈 Total iterations so far: 1560\n   🔍 Interrupted flag: False\nEpoch #25: loss=0.12755480135480562\n   📈 Incremented self.n_epochs to: 26\n\n📅 Starting Epoch 26\n🔍 Check termination: n_epochs=30, self.n_epochs=26\n🔄 === DECL TRAINING LOOP (Epoch 26) ===\n🔄 Batch 20/60\n   📊 Loss: Total=-0.033 (Rec=0.803, Trip=0.599, Sep=-0.988)\n🔄 Batch 40/60\n   📊 Loss: Total=0.158 (Rec=1.161, Trip=0.654, Sep=-0.975)\n   📊 Batch 40: Temp=0.149, AvgLoss=0.2337\n🔄 Batch 60/60\n   📊 Loss: Total=-0.070 (Rec=0.756, Trip=0.479, Sep=-0.992)\n📊 DECL Epoch 26 completed: 60 batches processed\n\n📊 === EPOCH 26 COMPLETED ===\n   ⏱️ Epoch time: 4.280s\n   ⏱️ Total time so far: 126.986s\n   📈 Batches processed: 60\n   📈 Average loss: 0.210340\n   📈 Total iterations so far: 1620\n   🔍 Interrupted flag: False\nEpoch #26: loss=0.21033995399872463\n   📈 Incremented self.n_epochs to: 27\n\n📅 Starting Epoch 27\n🔍 Check termination: n_epochs=30, self.n_epochs=27\n🔄 === DECL TRAINING LOOP (Epoch 27) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.648 (Rec=0.607, Trip=0.436, Sep=0.601)\n🔄 Batch 40/60\n   📊 Loss: Total=0.695 (Rec=2.211, Trip=0.682, Sep=-0.958)\n   📊 Batch 40: Temp=0.135, AvgLoss=0.2150\n🔄 Batch 60/60\n   📊 Loss: Total=0.214 (Rec=1.265, Trip=0.677, Sep=-0.972)\n📊 DECL Epoch 27 completed: 60 batches processed\n\n📊 === EPOCH 27 COMPLETED ===\n   ⏱️ Epoch time: 4.297s\n   ⏱️ Total time so far: 131.284s\n   📈 Batches processed: 60\n   📈 Average loss: 0.179244\n   📈 Total iterations so far: 1680\n   🔍 Interrupted flag: False\nEpoch #27: loss=0.1792443851629893\n   📈 Incremented self.n_epochs to: 28\n\n📅 Starting Epoch 28\n🔍 Check termination: n_epochs=30, self.n_epochs=28\n🔄 === DECL TRAINING LOOP (Epoch 28) ===\n🔄 Batch 20/60\n   📊 Loss: Total=0.988 (Rec=1.181, Trip=0.691, Sep=0.657)\n🔄 Batch 40/60\n   📊 Loss: Total=0.281 (Rec=0.784, Trip=0.632, Sep=-0.349)\n   📊 Batch 40: Temp=0.122, AvgLoss=0.2135\n🔄 Batch 60/60\n   📊 Loss: Total=-0.025 (Rec=0.832, Trip=0.528, Sep=-0.987)\n📊 DECL Epoch 28 completed: 60 batches processed\n\n📊 === EPOCH 28 COMPLETED ===\n   ⏱️ Epoch time: 3.935s\n   ⏱️ Total time so far: 135.219s\n   📈 Batches processed: 60\n   📈 Average loss: 0.199252\n   📈 Total iterations so far: 1740\n   🔍 Interrupted flag: False\nEpoch #28: loss=0.19925207744042078\n   📈 Incremented self.n_epochs to: 29\n\n📅 Starting Epoch 29\n🔍 Check termination: n_epochs=30, self.n_epochs=29\n🔄 === DECL TRAINING LOOP (Epoch 29) ===\n🔄 Batch 20/60\n   📊 Loss: Total=-0.063 (Rec=0.748, Trip=0.544, Sep=-0.982)\n🔄 Batch 40/60\n   📊 Loss: Total=-0.072 (Rec=0.748, Trip=0.488, Sep=-0.990)\n   📊 Batch 40: Temp=0.111, AvgLoss=0.1142\n🔄 Batch 60/60\n   📊 Loss: Total=0.118 (Rec=0.655, Trip=0.459, Sep=-0.511)\n📊 DECL Epoch 29 completed: 60 batches processed\n\n📊 === EPOCH 29 COMPLETED ===\n   ⏱️ Epoch time: 4.186s\n   ⏱️ Total time so far: 139.404s\n   📈 Batches processed: 60\n   📈 Average loss: 0.167060\n   📈 Total iterations so far: 1800\n   🔍 Interrupted flag: False\nEpoch #29: loss=0.16705978214740752\n   📈 Incremented self.n_epochs to: 30\n\n📅 Starting Epoch 30\n🔍 Check termination: n_epochs=30, self.n_epochs=30\n🛑 Breaking due to epoch limit: 30 >= 30\n\n🏁 === TRAINING COMPLETED ===\n   ⏱️ Total training time: 139.404s\n   📈 Total epochs completed: 30\n   📈 Total iterations completed: 1800\n   📈 Final loss: 0.16705978214740752\n   📊 Loss history: [0.5667554408311843, 0.41731854851047195, 0.3611666222413381, 0.25659243141611415, 0.23344631791114806, 0.3388175770640373, 0.24173940643668174, 0.2653447854022185, 0.1886412022014459, 0.1879933958252271, 0.19445992062489192, 0.2237669085462888, 0.1956522027651469, 0.2501670668522517, 0.206346798936526, 0.30785027196009956, 0.20619765520095826, 0.23456203937530518, 0.21006558587153754, 0.25719042122364044, 0.2206266090273857, 0.20687172412872315, 0.1814699669679006, 0.1772689829270045, 0.19845669368902843, 0.12755480135480562, 0.21033995399872463, 0.1792443851629893, 0.19925207744042078, 0.16705978214740752]\n\nTraining time: 0:02:19.934009\n\nEvaluation result: {'f1': 0.42938931297709926, 'precision': 0.3223495702005731, 'recall': 0.6428571428571429, 'infer_time': 27.146411895751953}\nFinished.\n", "stderr": "/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/pywt/_thresholding.py:23: RuntimeWarning: invalid value encountered in true_divide\n  thresholded = (1 - value/magnitude)\n"}, {"reconstruct_target": "denoised", "description": "方案B: 重建去噪信号 (审稿人建议)", "f1_score": null, "precision": null, "recall": null, "success": true, "stdout": "Dataset: ConstPos\nArguments: Namespace(batch_size=8, dataset='ConstPos', epochs=30, eval=True, gpu=0, gumbel_tau=1.0, irregular=0, iters=None, lambda_align=0.1, lambda_decl=0.1, lambda_sep=0.5, lmd=0.1, load_tp=False, loader='user_anomaly', lr=0.0001, mask_ratio_per_seg=0.05, max_threads=None, max_train_length=3000, noise_amplification_factor=2.0, reconstruct_target='denoised', repr_dims=320, run_name='reconstruct_denoised_experiment', save_every=None, seed=None, segment_num=3, sgd=False, tc_timesteps=5, temp=1.0, temp_contrast=1.0)\nLoading data... done\n(480, 50, 37)\n\n🔍 === TRAINING FLOW INVESTIGATION ===\n📊 Input train_data type: <class 'dict'>\n📊 Input train_data keys: dict_keys(['x', 'x_denoised', 'x_noisy', 'mask'])\n🎯 DECL Mode Detected: True\n🔄 === DECL TRAINING BRANCH ===\n📈 Original data shape: (480, 50, 37)\n📈 Denoised data shape: (480, 50, 37)\n📈 Noisy data shape: (480, 50, 37)\n📈 Mask shape: (480, 50, 36)\n📈 Batch size: 8\n🎯 初始化Gumbel-Softmax门控网络...\n🚀 初始化基于Gumbel-Softmax的自适应去噪系统...\n✅ 自适应去噪模块已创建\n   - 输入维度: 36\n   - 去噪器数量: 7\n   - 可用去噪器: ['m_0', 'm_1', 'g_2', 'g_3', 's_4', 'm_5', 'w_6']\n✅ 门控网络初始化完成\n🌡️ 温度退火设置: 初始2.0 -> 最终0.1\n📊 DataLoader created with 60 batches\n📊 Expected batches per epoch: 60\n🔧 DECL Optimizer created with 64 parameters (包含门控网络)\n\n🎯 === TRAINING PARAMETERS INVESTIGATION ===\n📊 Input n_epochs: 30\n📊 Input n_iters: None\n📊 Final training parameters:\n   - n_epochs: 30\n   - n_iters: None\n   - max_epochs: 30\n   - batches_per_epoch: 60\n   - self.n_epochs: 0\n   - self.n_iters: 0\n\n🚀 === STARTING TRAINING LOOP ===\n\n📅 Starting Epoch 0\n🔍 Check termination: n_epochs=30, self.n_epochs=0\n🔄 === DECL TRAINING LOOP (Epoch 0) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=2.000, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 0 completed: 60 batches processed\n\n📊 === EPOCH 0 COMPLETED ===\n   ⏱️ Epoch time: 4.689s\n   ⏱️ Total time so far: 4.689s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 60\n   🔍 Interrupted flag: False\nEpoch #0: loss=nan\n   📈 Incremented self.n_epochs to: 1\n\n📅 Starting Epoch 1\n🔍 Check termination: n_epochs=30, self.n_epochs=1\n🔄 === DECL TRAINING LOOP (Epoch 1) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=1.810, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 1 completed: 60 batches processed\n\n📊 === EPOCH 1 COMPLETED ===\n   ⏱️ Epoch time: 4.641s\n   ⏱️ Total time so far: 9.330s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 120\n   🔍 Interrupted flag: False\nEpoch #1: loss=nan\n   📈 Incremented self.n_epochs to: 2\n\n📅 Starting Epoch 2\n🔍 Check termination: n_epochs=30, self.n_epochs=2\n🔄 === DECL TRAINING LOOP (Epoch 2) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=1.638, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 2 completed: 60 batches processed\n\n📊 === EPOCH 2 COMPLETED ===\n   ⏱️ Epoch time: 4.184s\n   ⏱️ Total time so far: 13.514s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 180\n   🔍 Interrupted flag: False\nEpoch #2: loss=nan\n   📈 Incremented self.n_epochs to: 3\n\n📅 Starting Epoch 3\n🔍 Check termination: n_epochs=30, self.n_epochs=3\n🔄 === DECL TRAINING LOOP (Epoch 3) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=1.482, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 3 completed: 60 batches processed\n\n📊 === EPOCH 3 COMPLETED ===\n   ⏱️ Epoch time: 4.562s\n   ⏱️ Total time so far: 18.075s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 240\n   🔍 Interrupted flag: False\nEpoch #3: loss=nan\n   📈 Incremented self.n_epochs to: 4\n\n📅 Starting Epoch 4\n🔍 Check termination: n_epochs=30, self.n_epochs=4\n🔄 === DECL TRAINING LOOP (Epoch 4) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=1.341, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 4 completed: 60 batches processed\n\n📊 === EPOCH 4 COMPLETED ===\n   ⏱️ Epoch time: 4.515s\n   ⏱️ Total time so far: 22.590s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 300\n   🔍 Interrupted flag: False\nEpoch #4: loss=nan\n   📈 Incremented self.n_epochs to: 5\n\n📅 Starting Epoch 5\n🔍 Check termination: n_epochs=30, self.n_epochs=5\n🔄 === DECL TRAINING LOOP (Epoch 5) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=1.214, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 5 completed: 60 batches processed\n\n📊 === EPOCH 5 COMPLETED ===\n   ⏱️ Epoch time: 4.497s\n   ⏱️ Total time so far: 27.087s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 360\n   🔍 Interrupted flag: False\nEpoch #5: loss=nan\n   📈 Incremented self.n_epochs to: 6\n\n📅 Starting Epoch 6\n🔍 Check termination: n_epochs=30, self.n_epochs=6\n🔄 === DECL TRAINING LOOP (Epoch 6) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=1.099, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 6 completed: 60 batches processed\n\n📊 === EPOCH 6 COMPLETED ===\n   ⏱️ Epoch time: 4.524s\n   ⏱️ Total time so far: 31.611s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 420\n   🔍 Interrupted flag: False\nEpoch #6: loss=nan\n   📈 Incremented self.n_epochs to: 7\n\n📅 Starting Epoch 7\n🔍 Check termination: n_epochs=30, self.n_epochs=7\n🔄 === DECL TRAINING LOOP (Epoch 7) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.994, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 7 completed: 60 batches processed\n\n📊 === EPOCH 7 COMPLETED ===\n   ⏱️ Epoch time: 4.295s\n   ⏱️ Total time so far: 35.906s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 480\n   🔍 Interrupted flag: False\nEpoch #7: loss=nan\n   📈 Incremented self.n_epochs to: 8\n\n📅 Starting Epoch 8\n🔍 Check termination: n_epochs=30, self.n_epochs=8\n🔄 === DECL TRAINING LOOP (Epoch 8) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.900, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 8 completed: 60 batches processed\n\n📊 === EPOCH 8 COMPLETED ===\n   ⏱️ Epoch time: 4.113s\n   ⏱️ Total time so far: 40.019s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 540\n   🔍 Interrupted flag: False\nEpoch #8: loss=nan\n   📈 Incremented self.n_epochs to: 9\n\n📅 Starting Epoch 9\n🔍 Check termination: n_epochs=30, self.n_epochs=9\n🔄 === DECL TRAINING LOOP (Epoch 9) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.814, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 9 completed: 60 batches processed\n\n📊 === EPOCH 9 COMPLETED ===\n   ⏱️ Epoch time: 4.317s\n   ⏱️ Total time so far: 44.335s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 600\n   🔍 Interrupted flag: False\nEpoch #9: loss=nan\n   📈 Incremented self.n_epochs to: 10\n\n📅 Starting Epoch 10\n🔍 Check termination: n_epochs=30, self.n_epochs=10\n🔄 === DECL TRAINING LOOP (Epoch 10) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.737, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 10 completed: 60 batches processed\n\n📊 === EPOCH 10 COMPLETED ===\n   ⏱️ Epoch time: 4.226s\n   ⏱️ Total time so far: 48.561s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 660\n   🔍 Interrupted flag: False\nEpoch #10: loss=nan\n   📈 Incremented self.n_epochs to: 11\n\n📅 Starting Epoch 11\n🔍 Check termination: n_epochs=30, self.n_epochs=11\n🔄 === DECL TRAINING LOOP (Epoch 11) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.667, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 11 completed: 60 batches processed\n\n📊 === EPOCH 11 COMPLETED ===\n   ⏱️ Epoch time: 4.471s\n   ⏱️ Total time so far: 53.032s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 720\n   🔍 Interrupted flag: False\nEpoch #11: loss=nan\n   📈 Incremented self.n_epochs to: 12\n\n📅 Starting Epoch 12\n🔍 Check termination: n_epochs=30, self.n_epochs=12\n🔄 === DECL TRAINING LOOP (Epoch 12) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.603, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 12 completed: 60 batches processed\n\n📊 === EPOCH 12 COMPLETED ===\n   ⏱️ Epoch time: 4.613s\n   ⏱️ Total time so far: 57.646s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 780\n   🔍 Interrupted flag: False\nEpoch #12: loss=nan\n   📈 Incremented self.n_epochs to: 13\n\n📅 Starting Epoch 13\n🔍 Check termination: n_epochs=30, self.n_epochs=13\n🔄 === DECL TRAINING LOOP (Epoch 13) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.546, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 13 completed: 60 batches processed\n\n📊 === EPOCH 13 COMPLETED ===\n   ⏱️ Epoch time: 4.344s\n   ⏱️ Total time so far: 61.990s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 840\n   🔍 Interrupted flag: False\nEpoch #13: loss=nan\n   📈 Incremented self.n_epochs to: 14\n\n📅 Starting Epoch 14\n🔍 Check termination: n_epochs=30, self.n_epochs=14\n🔄 === DECL TRAINING LOOP (Epoch 14) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.494, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 14 completed: 60 batches processed\n\n📊 === EPOCH 14 COMPLETED ===\n   ⏱️ Epoch time: 4.457s\n   ⏱️ Total time so far: 66.447s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 900\n   🔍 Interrupted flag: False\nEpoch #14: loss=nan\n   📈 Incremented self.n_epochs to: 15\n\n📅 Starting Epoch 15\n🔍 Check termination: n_epochs=30, self.n_epochs=15\n🔄 === DECL TRAINING LOOP (Epoch 15) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.447, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 15 completed: 60 batches processed\n\n📊 === EPOCH 15 COMPLETED ===\n   ⏱️ Epoch time: 4.108s\n   ⏱️ Total time so far: 70.555s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 960\n   🔍 Interrupted flag: False\nEpoch #15: loss=nan\n   📈 Incremented self.n_epochs to: 16\n\n📅 Starting Epoch 16\n🔍 Check termination: n_epochs=30, self.n_epochs=16\n🔄 === DECL TRAINING LOOP (Epoch 16) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.405, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 16 completed: 60 batches processed\n\n📊 === EPOCH 16 COMPLETED ===\n   ⏱️ Epoch time: 4.292s\n   ⏱️ Total time so far: 74.847s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1020\n   🔍 Interrupted flag: False\nEpoch #16: loss=nan\n   📈 Incremented self.n_epochs to: 17\n\n📅 Starting Epoch 17\n🔍 Check termination: n_epochs=30, self.n_epochs=17\n🔄 === DECL TRAINING LOOP (Epoch 17) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.366, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 17 completed: 60 batches processed\n\n📊 === EPOCH 17 COMPLETED ===\n   ⏱️ Epoch time: 4.515s\n   ⏱️ Total time so far: 79.362s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1080\n   🔍 Interrupted flag: False\nEpoch #17: loss=nan\n   📈 Incremented self.n_epochs to: 18\n\n📅 Starting Epoch 18\n🔍 Check termination: n_epochs=30, self.n_epochs=18\n🔄 === DECL TRAINING LOOP (Epoch 18) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.331, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 18 completed: 60 batches processed\n\n📊 === EPOCH 18 COMPLETED ===\n   ⏱️ Epoch time: 4.268s\n   ⏱️ Total time so far: 83.630s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1140\n   🔍 Interrupted flag: False\nEpoch #18: loss=nan\n   📈 Incremented self.n_epochs to: 19\n\n📅 Starting Epoch 19\n🔍 Check termination: n_epochs=30, self.n_epochs=19\n🔄 === DECL TRAINING LOOP (Epoch 19) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.300, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 19 completed: 60 batches processed\n\n📊 === EPOCH 19 COMPLETED ===\n   ⏱️ Epoch time: 4.505s\n   ⏱️ Total time so far: 88.135s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1200\n   🔍 Interrupted flag: False\nEpoch #19: loss=nan\n   📈 Incremented self.n_epochs to: 20\n\n📅 Starting Epoch 20\n🔍 Check termination: n_epochs=30, self.n_epochs=20\n🔄 === DECL TRAINING LOOP (Epoch 20) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.271, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 20 completed: 60 batches processed\n\n📊 === EPOCH 20 COMPLETED ===\n   ⏱️ Epoch time: 4.395s\n   ⏱️ Total time so far: 92.530s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1260\n   🔍 Interrupted flag: False\nEpoch #20: loss=nan\n   📈 Incremented self.n_epochs to: 21\n\n📅 Starting Epoch 21\n🔍 Check termination: n_epochs=30, self.n_epochs=21\n🔄 === DECL TRAINING LOOP (Epoch 21) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.246, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 21 completed: 60 batches processed\n\n📊 === EPOCH 21 COMPLETED ===\n   ⏱️ Epoch time: 4.528s\n   ⏱️ Total time so far: 97.058s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1320\n   🔍 Interrupted flag: False\nEpoch #21: loss=nan\n   📈 Incremented self.n_epochs to: 22\n\n📅 Starting Epoch 22\n🔍 Check termination: n_epochs=30, self.n_epochs=22\n🔄 === DECL TRAINING LOOP (Epoch 22) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.222, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 22 completed: 60 batches processed\n\n📊 === EPOCH 22 COMPLETED ===\n   ⏱️ Epoch time: 4.392s\n   ⏱️ Total time so far: 101.450s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1380\n   🔍 Interrupted flag: False\nEpoch #22: loss=nan\n   📈 Incremented self.n_epochs to: 23\n\n📅 Starting Epoch 23\n🔍 Check termination: n_epochs=30, self.n_epochs=23\n🔄 === DECL TRAINING LOOP (Epoch 23) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.201, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 23 completed: 60 batches processed\n\n📊 === EPOCH 23 COMPLETED ===\n   ⏱️ Epoch time: 4.146s\n   ⏱️ Total time so far: 105.596s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1440\n   🔍 Interrupted flag: False\nEpoch #23: loss=nan\n   📈 Incremented self.n_epochs to: 24\n\n📅 Starting Epoch 24\n🔍 Check termination: n_epochs=30, self.n_epochs=24\n🔄 === DECL TRAINING LOOP (Epoch 24) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.182, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 24 completed: 60 batches processed\n\n📊 === EPOCH 24 COMPLETED ===\n   ⏱️ Epoch time: 4.252s\n   ⏱️ Total time so far: 109.848s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1500\n   🔍 Interrupted flag: False\nEpoch #24: loss=nan\n   📈 Incremented self.n_epochs to: 25\n\n📅 Starting Epoch 25\n🔍 Check termination: n_epochs=30, self.n_epochs=25\n🔄 === DECL TRAINING LOOP (Epoch 25) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.165, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 25 completed: 60 batches processed\n\n📊 === EPOCH 25 COMPLETED ===\n   ⏱️ Epoch time: 4.102s\n   ⏱️ Total time so far: 113.950s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1560\n   🔍 Interrupted flag: False\nEpoch #25: loss=nan\n   📈 Incremented self.n_epochs to: 26\n\n📅 Starting Epoch 26\n🔍 Check termination: n_epochs=30, self.n_epochs=26\n🔄 === DECL TRAINING LOOP (Epoch 26) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.149, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 26 completed: 60 batches processed\n\n📊 === EPOCH 26 COMPLETED ===\n   ⏱️ Epoch time: 4.119s\n   ⏱️ Total time so far: 118.069s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1620\n   🔍 Interrupted flag: False\nEpoch #26: loss=nan\n   📈 Incremented self.n_epochs to: 27\n\n📅 Starting Epoch 27\n🔍 Check termination: n_epochs=30, self.n_epochs=27\n🔄 === DECL TRAINING LOOP (Epoch 27) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.135, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 27 completed: 60 batches processed\n\n📊 === EPOCH 27 COMPLETED ===\n   ⏱️ Epoch time: 4.224s\n   ⏱️ Total time so far: 122.293s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1680\n   🔍 Interrupted flag: False\nEpoch #27: loss=nan\n   📈 Incremented self.n_epochs to: 28\n\n📅 Starting Epoch 28\n🔍 Check termination: n_epochs=30, self.n_epochs=28\n🔄 === DECL TRAINING LOOP (Epoch 28) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.122, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 28 completed: 60 batches processed\n\n📊 === EPOCH 28 COMPLETED ===\n   ⏱️ Epoch time: 4.078s\n   ⏱️ Total time so far: 126.371s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1740\n   🔍 Interrupted flag: False\nEpoch #28: loss=nan\n   📈 Incremented self.n_epochs to: 29\n\n📅 Starting Epoch 29\n🔍 Check termination: n_epochs=30, self.n_epochs=29\n🔄 === DECL TRAINING LOOP (Epoch 29) ===\n🔄 Batch 20/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n🔄 Batch 40/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n   📊 Batch 40: Temp=0.111, AvgLoss=nan\n🔄 Batch 60/60\n   📊 Loss: Total=nan (Rec=nan, Trip=nan, Sep=nan)\n📊 DECL Epoch 29 completed: 60 batches processed\n\n📊 === EPOCH 29 COMPLETED ===\n   ⏱️ Epoch time: 4.245s\n   ⏱️ Total time so far: 130.617s\n   📈 Batches processed: 60\n   📈 Average loss: nan\n   📈 Total iterations so far: 1800\n   🔍 Interrupted flag: False\nEpoch #29: loss=nan\n   📈 Incremented self.n_epochs to: 30\n\n📅 Starting Epoch 30\n🔍 Check termination: n_epochs=30, self.n_epochs=30\n🛑 Breaking due to epoch limit: 30 >= 30\n\n🏁 === TRAINING COMPLETED ===\n   ⏱️ Total training time: 130.617s\n   📈 Total epochs completed: 30\n   📈 Total iterations completed: 1800\n   📈 Final loss: nan\n   📊 Loss history: [nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan, nan]\n\nTraining time: 0:02:11.047843\n\nEvaluation result: {'f1': 0.7502679528403001, 'precision': 0.6003430531732419, 'recall': 1.0, 'infer_time': 25.083210229873657}\nFinished.\n", "stderr": "/home/<USER>/anaconda3/envs/tsrl/lib/python3.8/site-packages/pywt/_thresholding.py:23: RuntimeWarning: invalid value encountered in true_divide\n  thresholded = (1 - value/magnitude)\n"}]