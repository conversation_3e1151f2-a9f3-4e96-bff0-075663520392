import torch
import time
import numpy as np
import os
import sys

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

from timesurl import TimesURL

def benchmark_model_complexity():
    """基准测试模型复杂度和预期训练时间"""
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 数据规模
    batch_size = 8
    seq_len = 500
    n_features = 37
    
    print(f"\n=== 数据规模分析 ===")
    print(f"Batch size: {batch_size}")
    print(f"序列长度: {seq_len}")
    print(f"特征维度: {n_features}")
    print(f"每个样本数据点: {seq_len * n_features:,}")
    print(f"每个batch总数据点: {batch_size * seq_len * n_features:,}")
    
    # 创建测试数据
    test_data = torch.randn(batch_size, seq_len, n_features).to(device)
    test_mask = torch.ones(batch_size, seq_len, n_features-1).to(device)
    
    # 测试不同模型配置的时间
    configs = [
        {"hidden_dims": 32, "depth": 2, "name": "轻量级"},
        {"hidden_dims": 64, "depth": 4, "name": "标准配置"},
        {"hidden_dims": 128, "depth": 6, "name": "重型配置"}
    ]
    
    for config in configs:
        print(f"\n=== {config['name']} 模型测试 ===")
        
        try:
            from types import SimpleNamespace
            args = SimpleNamespace(tc_timesteps=5)
            
            model = TimesURL(
                input_dims=n_features-1,
                output_dims=config["hidden_dims"],
                hidden_dims=config["hidden_dims"],
                depth=config["depth"],
                device=device,
                args=args
            ).to(device)
            
            # 预热
            with torch.no_grad():
                _ = model._net({'data': test_data, 'mask': test_mask, 'mask_origin': test_mask})
            
            # 测试前向传播时间
            torch.cuda.synchronize() if device == 'cuda' else None
            start_time = time.time()
            
            for _ in range(10):  # 多次测试取平均
                with torch.no_grad():
                    output = model._net({'data': test_data, 'mask': test_mask, 'mask_origin': test_mask})
            
            torch.cuda.synchronize() if device == 'cuda' else None
            forward_time = (time.time() - start_time) / 10
            
            # 测试反向传播时间
            model.train()
            optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
            
            torch.cuda.synchronize() if device == 'cuda' else None
            start_time = time.time()
            
            for _ in range(5):
                optimizer.zero_grad()
                output = model._net({'data': test_data, 'mask': test_mask, 'mask_origin': test_mask})
                loss = output[0].mean()  # 简单损失
                loss.backward()
                optimizer.step()
            
            torch.cuda.synchronize() if device == 'cuda' else None
            backward_time = (time.time() - start_time) / 5
            
            print(f"  模型参数量: {sum(p.numel() for p in model.parameters()):,}")
            print(f"  前向传播时间: {forward_time:.4f}s")
            print(f"  完整训练步骤时间: {backward_time:.4f}s")
            print(f"  预期每epoch时间 (6 batches): {backward_time * 6:.4f}s")
            
            # 内存使用
            if device == 'cuda':
                memory_used = torch.cuda.max_memory_allocated() / 1024**2
                print(f"  GPU内存使用: {memory_used:.1f} MB")
                torch.cuda.reset_peak_memory_stats()
            
        except Exception as e:
            print(f"  测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    benchmark_model_complexity()
