import pandas as pd
import numpy as np

# 读取CSV文件
df = pd.read_csv('GridSybil.csv', header=None)

# 将数据集中最后一列命名为"Label"
df.rename(columns={df.columns[-1]: 'Label'}, inplace=True)

# 计算 DoSDisruptiveSybil 样本的数量
dos_disruptive_sybil_samples = df[df['Label'] == 'GridSybil']
dos_disruptive_sybil_count = len(dos_disruptive_sybil_samples)

# 从原数据集中删除 DoSDisruptiveSybil 样本
df = df[df['Label'] != 'GridSybil']

# # 定义组数 N 并计算每组样本的数量
# N = dos_disruptive_sybil_count // 10
# group_size = dos_disruptive_sybil_count // N
# group_size = min(max(group_size, 50), 200)  # 每组样本数限制在 50 到 200 之间

# 计算组数 N
group_size = np.random.randint(low=30, high=101)  # 随机选择组内样本数，限制在50到201之间
N = dos_disruptive_sybil_count // group_size  # 计算组数

# 处理余数
remainder = dos_disruptive_sybil_count % group_size
if remainder != 0:
    N += 1

# 随机打乱 DoSDisruptiveSybil 样本的顺序
# dos_disruptive_sybil_samples = dos_disruptive_sybil_samples.sample(frac=1, random_state=42)

# 将每组 DoSDisruptiveSybil 样本插入原数据集
for i in range(N):
    start_index = i * group_size
    end_index = (i + 1) * group_size
    dos_disruptive_sybil_group = dos_disruptive_sybil_samples[start_index:end_index]

    # 随机选择插入位置
    insert_index = np.random.randint(low=0, high=len(df))

    # 按照插入位置将组内的 DoSDisruptiveSybil 样本插入原数据集
    df = pd.concat([df.iloc[:insert_index], dos_disruptive_sybil_group, df.iloc[insert_index:]])

# 重置索引
df.reset_index(drop=True, inplace=True)


# 划分测试集和训练集
train_df = df.iloc[:12000]
test_df = df.iloc[12000:]

# 删除最后一列
train_df = train_df.iloc[:, :-1]

print("train_df.values.shape",train_df.values.shape)

# 保存训练集和测试集到文件
np.save('GridSybil_train.npy', train_df.values)


#分离出最后一列
last_column = test_df.iloc[:, -1]
last_column = last_column.map(lambda x: False if x in ["Genuine"] else True)

test_df = test_df.iloc[:, :-1]

print(train_df)
print(test_df)
print(last_column)



print("test_df.values.shape",test_df.values.shape)
print("last_column_values",last_column.values.shape)

# 保存测试集和测试集label到文件
np.save('GridSybil_test.npy', test_df.values)
np.save('GridSybil_test_label.npy', last_column.values)



