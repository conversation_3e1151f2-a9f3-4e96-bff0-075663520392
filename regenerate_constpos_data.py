#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成ConstPos数据集，使用50时间步长度
从原始30000条时序数据生成更多的训练样本
"""

import os
import pandas as pd
import numpy as np
import sys

# 添加src目录到Python路径
sys.path.append('src')

def denoise_data(data, window_size=5):
    """简单的滑动平均去噪"""
    return data.rolling(window=window_size, center=True).mean().fillna(data)

def add_noise_to_data(data, noise_level=0.01):
    """添加高斯噪声"""
    noise = np.random.normal(0, noise_level, data.shape)
    return data + noise

def regenerate_constpos_data():
    """重新生成ConstPos数据集"""
    
    print("🔄 重新生成ConstPos数据集...")
    print("使用新的时间步长度: 50 (原来是500)")
    
    # 1. 检查原始CSV文件
    csv_path = 'src/dataset/ConstPos/ConstPos.csv'
    
    if not os.path.exists(csv_path):
        print(f"❌ 未找到原始CSV文件: {csv_path}")
        print("请确保ConstPos.csv文件存在于 src/dataset/ConstPos/ 目录中")
        return False
    
    # 2. 加载原始数据
    try:
        df = pd.read_csv(csv_path, header=None)
        print(f"✅ 成功加载原始数据: {df.shape}")
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return False
    
    # 3. 预处理数据（去除标签列）
    if df.shape[1] == 0:
        print("❌ 数据为空")
        return False
    
    # 假设最后一列是标签
    features = df.iloc[:, :-1]  # 除最后一列外的所有列作为特征
    labels = df.iloc[:, -1]     # 最后一列作为标签
    
    print(f"📊 特征维度: {features.shape[1]}")
    print(f"📊 时序数据长度: {features.shape[0]}")
    print(f"📊 标签分布: {labels.value_counts().to_dict()}")
    
    # 4. 训练/测试分割
    train_split_ratio = 0.8
    train_size = int(len(df) * train_split_ratio)
    
    train_features = features.iloc[:train_size]
    test_features = features.iloc[train_size:]
    train_labels = labels.iloc[:train_size]
    test_labels = labels.iloc[train_size:]
    
    print(f"📊 训练集时序长度: {len(train_features)}")
    print(f"📊 测试集时序长度: {len(test_features)}")
    
    # 5. 执行去噪和增噪
    print("🔄 执行去噪和增噪处理...")
    train_denoised = denoise_data(train_features)
    train_noisy = add_noise_to_data(train_features, noise_level=0.01)
    
    # 6. 使用新的segment_length=50进行分割
    segment_length = 50
    print(f"🔄 使用segment_length={segment_length}进行数据分割...")
    
    def split_into_segments(arr, seg_len):
        """将时序数据分割成固定长度的片段"""
        n_segments = arr.shape[0] // seg_len
        if n_segments == 0:
            return arr.reshape(1, arr.shape[0], -1)
        
        segmented = arr[:n_segments * seg_len].reshape(n_segments, seg_len, -1)
        
        # 处理剩余部分
        remainder = arr[n_segments * seg_len:]
        if len(remainder) > 0:
            # Padding到segment_length
            padded = np.pad(remainder, ((0, seg_len - len(remainder)), (0, 0)), 
                          mode='constant', constant_values=0)
            segmented = np.vstack([segmented, padded[np.newaxis, ...]])
        
        return segmented
    
    def split_labels(labels_array, seg_len):
        """分割标签，每个片段取最常见的标签"""
        n_segments = len(labels_array) // seg_len
        if n_segments == 0:
            return np.array([labels_array.iloc[0]])
        
        segment_labels = []
        for i in range(n_segments):
            start_idx = i * seg_len
            end_idx = start_idx + seg_len
            segment_data = labels_array.iloc[start_idx:end_idx]
            # 取最常见的标签
            most_common = segment_data.mode().iloc[0] if len(segment_data.mode()) > 0 else segment_data.iloc[0]
            segment_labels.append(most_common)
        
        # 处理剩余部分
        remainder = labels_array.iloc[n_segments * seg_len:]
        if len(remainder) > 0:
            most_common = remainder.mode().iloc[0] if len(remainder.mode()) > 0 else remainder.iloc[0]
            segment_labels.append(most_common)
        
        return np.array(segment_labels)
    
    # 分割训练数据
    train_features_segments = split_into_segments(train_features.values, segment_length)
    train_denoised_segments = split_into_segments(train_denoised.values, segment_length)
    train_noisy_segments = split_into_segments(train_noisy.values, segment_length)
    
    # 分割测试数据
    test_features_segments = split_into_segments(test_features.values, segment_length)
    
    # 分割标签
    test_labels_segments = split_labels(test_labels, segment_length)
    
    print(f"✅ 分割完成!")
    print(f"📊 训练样本数: {train_features_segments.shape[0]} (原来可能只有几十个)")
    print(f"📊 训练样本形状: {train_features_segments.shape}")
    print(f"📊 测试样本数: {test_features_segments.shape[0]}")
    print(f"📊 测试样本形状: {test_features_segments.shape}")
    
    # 7. 保存数据
    output_dir = 'src/dataset/ConstPos'
    os.makedirs(output_dir, exist_ok=True)
    
    print("💾 保存处理后的数据...")
    
    np.save(f'{output_dir}/ConstPos_train.npy', train_features_segments)
    np.save(f'{output_dir}/ConstPos_train_denoised.npy', train_denoised_segments)
    np.save(f'{output_dir}/ConstPos_train_noisy.npy', train_noisy_segments)
    np.save(f'{output_dir}/ConstPos_test.npy', test_features_segments)
    np.save(f'{output_dir}/ConstPos_test_label.npy', test_labels_segments)
    
    print("✅ 数据保存完成!")
    print(f"📁 保存位置: {output_dir}/")
    
    # 8. 验证数据
    print("\n🔍 验证生成的数据...")
    try:
        from datautils import load_user_anomaly
        train_data, train_labels, test_data, test_labels = load_user_anomaly('ConstPos', load_tp=True)
        print(f"✅ 验证成功!")
        print(f"📊 新的训练数据形状: {train_data['x'].shape}")
        print(f"📊 新的测试数据形状: {test_data['x'].shape}")
        
        # 检查是否有显著改善
        old_samples = 48  # 原来的样本数
        new_samples = train_data['x'].shape[0]
        improvement = new_samples / old_samples
        
        print(f"🎯 样本数改善:")
        print(f"   原来: {old_samples} 个样本")
        print(f"   现在: {new_samples} 个样本")
        print(f"   改善倍数: {improvement:.1f}x")
        
        if improvement > 5:
            print("🎉 样本数显著增加！这应该能大幅改善训练效果")
        
        return True
        
    except Exception as e:
        print(f"⚠️ 验证时出现问题: {e}")
        print("但数据文件已经生成，可以尝试继续训练")
        return True

def main():
    """主函数"""
    print("🚀 ConstPos数据重新生成工具")
    print("=" * 50)
    
    success = regenerate_constpos_data()
    
    if success:
        print("\n🎯 接下来的步骤:")
        print("1. 运行训练命令测试新数据:")
        print("   python run_user_anomaly.py ConstPos")
        print("2. 观察训练样本数是否显著增加")
        print("3. 检查训练时间是否合理（不再异常快）")
        print("4. 验证模型性能是否改善")
    else:
        print("\n❌ 数据生成失败，请检查错误信息")

if __name__ == "__main__":
    main() 