import numpy as np
import torch
import torch.nn.functional as F


def dcrad_contrastive_loss(z_denoised, z_original, z_noisy, temp=1.0, lambda_align=0.1):
    """
    DCRAD完整对比损失 = 语义对比项 + 去噪方向对齐项
    
    Args:
        z_denoised: 去噪样本表示 (B, T, D)
        z_original: 原始样本表示 (B, T, D) 
        z_noisy: 增噪样本表示 (B, T, D)
        temp: 温度系数
        lambda_align: 方向对齐项权重
        
    Returns:
        total_loss: 总对比损失
    """
    # 确保表示被L2归一化
    z_denoised = F.normalize(z_denoised, dim=-1)
    z_original = F.normalize(z_original, dim=-1)
    z_noisy = F.normalize(z_noisy, dim=-1)
    
    # 第一项：语义对比项 (拉近原始-去噪，推远原始-增噪)
    pos_sim = torch.bmm(z_original, z_denoised.transpose(1, 2)) / temp  # (B, T, T)
    neg_sim = torch.bmm(z_original, z_noisy.transpose(1, 2)) / temp     # (B, T, T)
    
    # 只取对角线元素（对应的时间步）
    pos_scores = torch.diagonal(pos_sim, dim1=1, dim2=2)  # (B, T)
    neg_scores = torch.diagonal(neg_sim, dim1=1, dim2=2)  # (B, T)
    
    # 对比损失：log(exp(pos) / (exp(pos) + exp(neg)))
    # 使用数值稳定的log-sum-exp技巧
    max_scores = torch.max(pos_scores, neg_scores)
    stable_log_sum = max_scores + torch.log(torch.exp(pos_scores - max_scores) + torch.exp(neg_scores - max_scores))
    semantic_loss = -torch.mean(pos_scores - stable_log_sum)
    
    # 第二项：去噪方向对齐项 (要求原始样本落在去噪→增噪连线上)
    # 计算从增噪到去噪的向量 (理想去噪方向)
    denoising_direction = z_denoised - z_noisy  # (B, T, D)
    
    # 计算从增噪到原始的向量 (实际向量)
    actual_direction = z_original - z_noisy     # (B, T, D)
    
    # 方向对齐损失：最小化两个方向的夹角
    # 使用余弦相似度，越接近1越好，添加数值稳定性
    eps = 1e-8
    direction_similarity = F.cosine_similarity(denoising_direction, actual_direction, dim=-1, eps=eps)  # (B, T)
    # 将相似度钳制在合理范围内，避免极端值
    direction_similarity = torch.clamp(direction_similarity, -1 + eps, 1 - eps)
    direction_alignment_loss = torch.mean(1 - direction_similarity)  # 1-cos(θ)，最小化夹角
    
    # 总损失
    total_loss = semantic_loss + lambda_align * direction_alignment_loss
    
    return total_loss


def decl_triplet_loss(tensor_A_pos, tensor_B_org, tensor_C_neg):
    """保持向后兼容的简单三元组损失"""
    dot_poduct1 = torch.bmm(tensor_A_pos, tensor_B_org.transpose(1, 2))
    dot_poduct2 = torch.bmm(tensor_B_org, tensor_C_neg.transpose(1, 2))
    pos = torch.exp(dot_poduct1)
    neg = torch.exp(dot_poduct2)
    denominator = pos+neg
    loss = torch.mean(-torch.log(torch.div(pos, denominator)))
    return loss 