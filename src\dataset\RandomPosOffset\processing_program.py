import pandas as pd
import numpy as np
import os

def denoise_data(data, window_size=5):
    """
    使用滑动平均对数据进行去噪。
    为每一列（特征）独立计算。
    """
    return data.rolling(window=window_size, min_periods=1, center=True).mean()

def add_noise_to_data(data, noise_level=0.01):
    """
    向数据中添加高斯噪声。
    为每一列（特征）独立添加。
    """
    noise = np.random.normal(0, noise_level, data.shape)
    noisy_data = data + noise
    return noisy_data

def main():
    # --- 配置 ---
    csv_filename = 'RandomPosOffset.csv'
    base_output_name = 'RandomPosOffset'
    label_column = 'label'
    train_split_ratio = 0.7
    
    # 获取当前脚本所在的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    input_csv_path = os.path.join(current_dir, csv_filename)
    
    print(f"开始处理: {input_csv_path}")

    # 1. 加载数据
    try:
        df = pd.read_csv(input_csv_path)
        print(f"成功加载数据，形状: {df.shape}")
    except FileNotFoundError:
        print(f"错误: 在目录下未找到 {csv_filename}")
        return

    # 2. 分离标签和特征，并自动选择数值型特征
    if label_column not in df.columns:
        print(f"错误: 标签列 '{label_column}' 在CSV中未找到。")
        return
    
    labels = df[label_column]
    # 选择所有数值类型的列作为特征
    features = df.select_dtypes(include=np.number).drop(columns=[label_column], errors='ignore')
    
    print(f"已自动选择 {features.shape[1]} 个数值型特征。")

    # 3. 训练/测试数据分割
    train_size = int(len(df) * train_split_ratio)
    
    train_features_df = features.iloc[:train_size]
    test_features_df = features.iloc[train_size:]
    test_labels = labels.iloc[train_size:]
    
    print(f"数据已分割: 训练集大小 {len(train_features_df)}, 测试集大小 {len(test_features_df)}")

    # 4. 执行去噪和增噪
    train_denoised_df = denoise_data(train_features_df)
    train_noisy_df = add_noise_to_data(train_features_df)
    print("已完成去噪和增噪处理。")

    # 5. 转换为Numpy并调整形状为 (1, timesteps, features)
    train_features_np = train_features_df.to_numpy()[np.newaxis, :, :]
    train_denoised_np = train_denoised_df.to_numpy()[np.newaxis, :, :]
    train_noisy_np = train_noisy_df.to_numpy()[np.newaxis, :, :]
    test_features_np = test_features_df.to_numpy()[np.newaxis, :, :]
    test_labels_np = test_labels.to_numpy()

    # 6. 保存为 .npy 文件到当前目录
    base_path = os.path.join(current_dir, base_output_name)
    np.save(f'{base_path}_train.npy', train_features_np)
    np.save(f'{base_path}_train_denoised.npy', train_denoised_np)
    np.save(f'{base_path}_train_noisy.npy', train_noisy_np)
    np.save(f'{base_path}_test.npy', test_features_np)
    np.save(f'{base_path}_test_label.npy', test_labels_np)
    
    print("\n所有文件已成功保存到当前目录:")
    print(f"  - {base_output_name}_train.npy (形状: {train_features_np.shape})")
    print(f"  - {base_output_name}_train_denoised.npy (形状: {train_denoised_np.shape})")
    print(f"  - {base_output_name}_train_noisy.npy (形状: {train_noisy_np.shape})")
    print(f"  - {base_output_name}_test.npy (形状: {test_features_np.shape})")
    print(f"  - {base_output_name}_test_label.npy (形状: {test_labels_np.shape})")

if __name__ == '__main__':
    main() 