#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户数据加载功能
"""

import sys
import os
import numpy as np

# 添加src目录到Python路径
sys.path.append('src')

try:
    import datautils
    print("✅ 成功导入 datautils")
except ImportError as e:
    print(f"❌ 导入 datautils 失败: {e}")
    sys.exit(1)

def test_load_user_anomaly():
    """测试load_user_anomaly函数"""
    
    print("测试用户异常检测数据加载...")
    
    dataset_name = 'ConstPos'
    
    try:
        # 测试数据加载 - 修复接口：load_user_anomaly只返回4个值
        train_data_task, train_labels, test_data, test_labels = datautils.load_user_anomaly(dataset_name, load_tp=True)
        
        print(f"✅ 成功加载数据集: {dataset_name}")
        print(f"✅ 训练数据形状: {train_data_task['x'].shape}")
        print(f"✅ 测试数据形状: {test_data['x'].shape}")
        
        # 检查DECL相关数据
        if 'x_denoised' in train_data_task:
            print(f"✅ 去噪训练数据形状: {train_data_task['x_denoised'].shape}")
        if 'x_noisy' in train_data_task:
            print(f"✅ 加噪训练数据形状: {train_data_task['x_noisy'].shape}")
        
        print(f"✅ 训练标签数量: {len(train_labels)}")
        print(f"✅ 测试标签数量: {len(test_labels)}")
        
        # 检查标签格式
        if len(test_labels) > 0:
            print(f"✅ 测试标签示例: {test_labels[:5]}")
            print(f"✅ 标签类型: {type(test_labels[0])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_preprocessing():
    """测试数据预处理"""
    
    print("\n测试数据预处理...")
    
    try:
        dataset_name = 'ConstPos'
        train_data_task, train_labels, test_data, test_labels = datautils.load_user_anomaly(dataset_name, load_tp=True)
        
        # 用户数据集已经是数组格式，不需要gen_ano_train_data转换
        print(f"✅ 训练数据已经是正确格式: {train_data_task['x'].shape}")
        print(f"✅ 训练数据mask形状: {train_data_task['mask'].shape}")
        
        # 检查DECL数据是否存在
        if 'x_denoised' in train_data_task and 'x_noisy' in train_data_task:
            print(f"✅ DECL去噪数据形状: {train_data_task['x_denoised'].shape}")
            print(f"✅ DECL加噪数据形状: {train_data_task['x_noisy'].shape}")
        
        # 检查数据一致性
        assert train_data_task['x'].shape[0] == train_data_task['mask'].shape[0], "训练数据和mask的样本数不一致"
        assert train_data_task['x'].shape[1] == train_data_task['mask'].shape[1], "训练数据和mask的时间步数不一致"
        print(f"✅ 数据一致性检查通过")
        
        # 检查测试数据
        print(f"✅ 测试数据形状: {test_data['x'].shape}")
        print(f"✅ 测试数据mask形状: {test_data['mask'].shape}")
        
        # 检查标签
        print(f"✅ 训练标签范围: [{np.min(train_labels)}, {np.max(train_labels)}]")
        if isinstance(test_labels[0], str):
            print(f"✅ 测试标签为字符串格式，唯一值: {np.unique(test_labels)}")
        else:
            print(f"✅ 测试标签范围: [{np.min(test_labels)}, {np.max(test_labels)}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据预处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("TimesURL 用户数据加载测试")
    print("=" * 40)
    
    # 检查数据集是否存在
    dataset_path = 'src/dataset/ConstPos'
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return
    
    # 检查必需文件
    required_files = [
        'ConstPos_train.npy',
        'ConstPos_test.npy',
        'ConstPos_test_label.npy'
    ]
    
    for file_name in required_files:
        file_path = os.path.join(dataset_path, file_name)
        if not os.path.exists(file_path):
            print(f"❌ 缺少文件: {file_path}")
            return
    
    print("✅ 所有必需文件都存在")
    
    # 运行测试
    success1 = test_load_user_anomaly()
    success2 = test_data_preprocessing()
    
    if success1 and success2:
        print("\n✅ 所有测试通过! 数据加载功能正常工作。")
        print("\n下一步:")
        print("  运行训练: python run_user_anomaly.py ConstPos")
        print("  或者: python src/train.py ConstPos test_run --loader user_anomaly --eval")
    else:
        print("\n❌ 一些测试失败，请检查错误信息")

if __name__ == "__main__":
    main() 