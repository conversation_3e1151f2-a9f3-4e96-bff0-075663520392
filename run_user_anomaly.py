#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TimesURL 用户数据集运行示例脚本

此脚本演示如何使用用户的异常检测数据集训练TimesURL模型。
"""

import os
import sys
import subprocess
import argparse

def run_training(dataset_name, run_name, **kwargs):
    """运行训练"""
    
    # 基础命令
    cmd = [
        sys.executable, 'src/train.py',
        dataset_name,
        run_name,
        '--loader', 'user_anomaly',
        '--eval'  # 默认启用评估
    ]
    
    # 添加可选参数
    if kwargs.get('epochs'):
        cmd.extend(['--epochs', str(kwargs['epochs'])])
    
    if kwargs.get('batch_size'):
        cmd.extend(['--batch-size', str(kwargs['batch_size'])])
    
    if kwargs.get('lr'):
        cmd.extend(['--lr', str(kwargs['lr'])])
    
    if kwargs.get('gpu') is not None:
        cmd.extend(['--gpu', str(kwargs['gpu'])])
    
    print("执行命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 运行训练
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行训练时出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TimesURL 用户数据集训练脚本')
    
    parser.add_argument('dataset', help='数据集名称 (例如: ConstPos)')
    parser.add_argument('--run-name', default='user_experiment', 
                       help='运行名称，用于保存模型和结果 (默认: user_experiment)')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=5, 
                       help='训练轮数 (默认: 100)')
    parser.add_argument('--batch-size', type=int, default=8, 
                       help='批次大小 (默认: 8)')
    parser.add_argument('--lr', type=float, default=0.0001, 
                       help='学习率 (默认: 0.0001)')
    parser.add_argument('--gpu', type=int, default=0, 
                       help='GPU编号 (默认: 0)')
    
    # 解析参数
    args = parser.parse_args()
    
    print("TimesURL 用户数据集训练")
    print("=" * 40)
    print(f"数据集: {args.dataset}")
    print(f"运行名称: {args.run_name}")
    print(f"训练参数: epochs={args.epochs}, batch_size={args.batch_size}, lr={args.lr}")
    print()
    
    # 检查数据集是否存在
    dataset_path = f'src/dataset/{args.dataset}'
    if not os.path.exists(dataset_path):
        print(f"❌ 错误: 数据集路径不存在: {dataset_path}")
        print("请确保数据集在正确的位置，或运行 'python setup_user_dataset.py' 检查数据集")
        return
    
    print("✅ 数据集文件检查通过")
    print()
    
    # 运行训练
    success = run_training(
        args.dataset,
        args.run_name,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr,
        gpu=args.gpu
    )
    
    if success:
        print("\n✅ 训练完成!")
        print(f"结果保存在: training/{args.dataset}__{args.run_name}_<timestamp>/")
    else:
        print("\n❌ 训练失败")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("使用方法: python run_user_anomaly.py <数据集名称> [选项]")
        print("示例: python run_user_anomaly.py ConstPos")
        print("获取帮助: python run_user_anomaly.py --help")
    else:
        main() 