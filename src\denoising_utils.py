import numpy as np
import torch
from scipy import signal
from sklearn.preprocessing import StandardScaler

from scipy.signal import butter, filtfilt
from pykalman import KalmanFilter
import pywt


def denoise_with_moving_average(data, window_size=5):
    """
    使用滑动平均对数据进行去噪
    
    Args:
        data (np.ndarray): 输入数据，形状为 (n_samples, n_timesteps, n_features)
        window_size (int): 滑动窗口大小
        
    Returns:
        np.ndarray: 去噪后的数据
    """
    if data.ndim == 3:
        # 对每个样本和特征独立处理
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                # 跳过时间位置向量（最后一个特征）
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]  # 保持时间位置不变
                else:
                    # 对信号特征进行滑动平均
                    padded = np.pad(data[i, :, j], (window_size//2, window_size//2), mode='edge')
                    denoised_data[i, :, j] = np.convolve(padded, np.ones(window_size)/window_size, mode='valid')
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")


def denoise_with_gaussian_filter(data, sigma=1.0):
    """
    使用高斯滤波器进行去噪
    
    Args:
        data (np.ndarray): 输入数据，形状为 (n_samples, n_timesteps, n_features)
        sigma (float): 高斯核的标准差
        
    Returns:
        np.ndarray: 去噪后的数据
    """
    from scipy.ndimage import gaussian_filter1d
    
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]  # 保持时间位置不变
                else:
                    denoised_data[i, :, j] = gaussian_filter1d(data[i, :, j], sigma=sigma)
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")


def denoise_with_savgol_filter(data, window_length=5, polyorder=2):
    """
    使用Savitzky-Golay滤波器进行去噪
    
    Args:
        data (np.ndarray): 输入数据，形状为 (n_samples, n_timesteps, n_features)
        window_length (int): 滤波器窗口长度（必须为奇数）
        polyorder (int): 多项式拟合的阶数
        
    Returns:
        np.ndarray: 去噪后的数据
    """
    if window_length % 2 == 0:
        window_length += 1  # 确保窗口长度为奇数
    
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]  # 保持时间位置不变
                else:
                    if data.shape[1] >= window_length:
                        denoised_data[i, :, j] = signal.savgol_filter(data[i, :, j], window_length, polyorder)
                    else:
                        denoised_data[i, :, j] = data[i, :, j]  # 如果数据太短则不滤波
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")


def compute_snr(original, denoised):
    """
    计算信噪比 (SNR)
    """
    signal_power = np.mean(original ** 2)
    noise = original - denoised
    noise_power = np.mean(noise ** 2)
    if signal_power == 0:
        return 0 if noise_power == 0 else -100
    if noise_power == 0:
        return 100
    return 10 * np.log10(signal_power / noise_power)

def compute_smoothness(denoised):
    """
    计算平滑度 (一阶差分方差的负值, 更高分数表示更平滑)
    """
    if denoised.shape[1] < 2:
        return 0
    diff = np.diff(denoised, axis=1)
    variance = np.mean(diff ** 2, axis=1)
    return -np.mean(variance)  # 负值以便更高分数更好

def denoise_with_kalman(data, process_noise=0.1, measurement_noise=1.0):
    """
    使用 Kalman 滤波器进行去噪 (经典于 IoV 轨迹平滑)
    """
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]  # 保持时间位置不变
                else:
                    kf = KalmanFilter(transition_matrices=[1], observation_matrices=[1],
                                      transition_covariance=process_noise,
                                      observation_covariance=measurement_noise)
                    measurements = data[i, :, j]
                    denoised, _ = kf.smooth(measurements)
                    denoised_data[i, :, j] = denoised.flatten()
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")

def denoise_with_wavelet(data, wavelet='db4', level=1):
    """
    使用小波变换进行去噪 (适合 IoV 非平稳噪声)
    """
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]
                else:
                    coeffs = pywt.wavedec(data[i, :, j], wavelet, level=level)
                    sigma = np.median(np.abs(coeffs[-level])) / 0.6745
                    thresh = sigma * np.sqrt(2 * np.log(len(data[i, :, j])))
                    coeffs[1:] = [pywt.threshold(c, thresh, mode='soft') for c in coeffs[1:]]
                    denoised_data[i, :, j] = pywt.waverec(coeffs, wavelet)
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")

def denoise_with_median_filter(data, kernel_size=3):
    """
    使用中值滤波进行去噪 (有效去除 IoV 尖峰噪声)
    """
    from scipy.ndimage import median_filter
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]
                else:
                    denoised_data[i, :, j] = median_filter(data[i, :, j], size=kernel_size)
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")

def denoise_with_lowpass(data, cutoff=0.1, fs=1.0, order=5):
    """
    使用 Butterworth 低通滤波进行去噪 (适合 IoV 高频干扰)
    """
    if data.ndim == 3:
        denoised_data = np.zeros_like(data)
        for i in range(data.shape[0]):
            for j in range(data.shape[2]):
                if j == data.shape[2] - 1:
                    denoised_data[i, :, j] = data[i, :, j]
                else:
                    b, a = butter(order, cutoff, fs=fs, btype='low', analog=False)
                    denoised_data[i, :, j] = filtfilt(b, a, data[i, :, j])
        return denoised_data
    else:
        raise ValueError(f"Expected 3D data, got {data.ndim}D")


def adaptive_denoise(data, methods=None, weights={'snr': 0.7, 'smoothness': 0.3}, 
                    fallback_method='moving_average'):
    """
    自适应去噪: 尝试多种方法, 选择分数最高的
    
    Args:
        data (np.ndarray): 输入数据 (n_samples, n_timesteps, n_features)
        methods (list): 要尝试的方法列表
        weights (dict): 分数权重
        fallback_method (str): 备用方法
        
    Returns:
        np.ndarray: 最佳去噪数据
    """
    if methods is None:
        methods = [
            ('moving_average', {'window_size': 3}),
            ('moving_average', {'window_size': 5}),
            ('gaussian', {'sigma': 0.8}),
            ('gaussian', {'sigma': 1.2}),
            ('savgol', {'window_length': 5, 'polyorder': 2}),
            ('kalman', {'process_noise': 0.05, 'measurement_noise': 0.8}),
            ('median', {'kernel_size': 3}),
        ]
    
    best_denoised = np.zeros_like(data)
    
    for i in range(data.shape[0]):
        best_score = -np.inf
        best_method_data = None
        
        for method_name, params in methods:
            try:
                # 应用去噪方法
                if method_name == 'moving_average':
                    candidate = denoise_with_moving_average(data[i:i+1], **params)
                elif method_name == 'gaussian':
                    candidate = denoise_with_gaussian_filter(data[i:i+1], **params)
                elif method_name == 'savgol':
                    candidate = denoise_with_savgol_filter(data[i:i+1], **params)
                elif method_name == 'kalman':
                    candidate = denoise_with_kalman(data[i:i+1], **params)
                elif method_name == 'median':
                    candidate = denoise_with_median_filter(data[i:i+1], **params)
                else:
                    continue
                
                # 计算评分 (排除时间位置特征)
                signal_features = data.shape[2] - 1  # 排除最后一个时间位置特征
                
                snr_scores = []
                smoothness_scores = []
                
                for j in range(signal_features):
                    snr = compute_snr(data[i, :, j], candidate[0, :, j])
                    smoothness = compute_smoothness(candidate[0, :, j:j+1])
                    
                    if not np.isnan(snr) and not np.isinf(snr):
                        snr_scores.append(snr)
                    if not np.isnan(smoothness) and not np.isinf(smoothness):
                        smoothness_scores.append(smoothness)
                
                if snr_scores and smoothness_scores:
                    avg_snr = np.mean(snr_scores)
                    avg_smoothness = np.mean(smoothness_scores)
                    score = weights['snr'] * avg_snr + weights['smoothness'] * avg_smoothness
                    
                    if score > best_score:
                        best_score = score
                        best_method_data = candidate
                        
            except Exception as e:
                continue
        
        # 如果没有找到合适的方法，使用备用方法
        if best_method_data is None:
            if fallback_method == 'moving_average':
                best_method_data = denoise_with_moving_average(data[i:i+1], window_size=5)
            elif fallback_method == 'gaussian':
                best_method_data = denoise_with_gaussian_filter(data[i:i+1], sigma=1.0)
            else:
                best_method_data = data[i:i+1]  # 原始数据作为最后备用
        
        # 修复：确保去噪结果不包含NaN
        denoised_sample = best_method_data[0]
        if np.isnan(denoised_sample).any() or np.isinf(denoised_sample).any():
            denoised_sample = data[i]  # 回退到原始数据
        
        best_denoised[i] = denoised_sample
    
    return best_denoised

# 更新 apply_denoising 以支持自适应
def apply_denoising(data, method='adaptive', **kwargs):
    if method == 'adaptive':
        return adaptive_denoise(data, **kwargs)
    elif method == 'moving_average':
        return denoise_with_moving_average(data, **kwargs)
    elif method == 'gaussian':
        return denoise_with_gaussian_filter(data, **kwargs)
    elif method == 'savgol':
        return denoise_with_savgol_filter(data, **kwargs)
    else:
        raise ValueError(f"Unknown denoising method: {method}")

def generate_decl_training_data(data, mask, noise_amplification_factor=2.0):
    """
    为DECL训练生成三元组数据: (增噪样本, 原始样本, 去噪样本)
    
    按照论文描述实现定向噪声放大策略：
    1. 使用自适应去噪策略生成高质量去噪版本
    2. 计算原始样本与去噪版本的残差，分离出最具干扰性的特定噪声分量
    3. 对特定噪声分量进行定向放大并重新叠加到原始样本上
    4. 生成包含清晰噪声递减梯度的三元组
    
    Args:
        data (np.ndarray): 原始数据 (n_samples, n_timesteps, n_features)
        mask (np.ndarray): 掩码数据
        noise_amplification_factor (float): 调节噪声放大强度的超参数 α
        
    Returns:
        dict: 包含 'x', 'x_denoised', 'x_noisy', 'mask' 的字典
              其中 x_noisy 是增噪样本，x 是原始样本，x_denoised 是去噪样本
    """
    # 1. 原始数据
    x_orig = data.copy()
    
    # 2. 去噪数据 - 使用自适应去噪策略生成高质量去噪版本（正样本）
    x_denoised = adaptive_denoise(data)
    
    # 修复：确保去噪数据不包含NaN或无穷值
    x_denoised = np.where(np.isnan(x_denoised), data, x_denoised)
    x_denoised = np.where(np.isinf(x_denoised), data, x_denoised)
    
    # 3. 定向噪声放大策略构建增噪样本
    x_noisy = data.copy()
    signal_features = data.shape[2] - 1  # 排除时间位置特征
    
    for i in range(data.shape[0]):
        for j in range(signal_features):  # 只对信号特征进行噪声放大
            # 步骤1：计算原始样本与去噪版本的残差，分离出特定噪声分量
            noise_component = x_orig[i, :, j] - x_denoised[i, :, j]
            
            # 步骤2：对特定噪声分量进行定向放大
            amplified_noise = noise_amplification_factor * noise_component
            
            # 步骤3：将放大后的噪声重新叠加到原始样本上，合成增噪样本
            x_noisy[i, :, j] = x_orig[i, :, j] + amplified_noise
    
    # 返回三元组：{增噪样本，原始样本，去噪样本}
    # 这个三元组内含清晰的噪声递减梯度，为对比学习提供结构化且高度可靠的信号
    return {
        'x': x_orig,           # 原始样本（中等噪声水平）
        'x_denoised': x_denoised,  # 去噪样本（低噪声水平，正样本）
        'x_noisy': x_noisy,    # 增噪样本（高噪声水平，负样本）
        'mask': mask
    }

def denoise_with_moving_average_optimized(data, window_size=5):
    """
    优化版本的移动平均去噪
    """
    return denoise_with_moving_average(data, window_size)

def denoise_with_gaussian_filter_optimized(data, sigma=1.0):
    """
    优化版本的高斯滤波去噪
    """
    return denoise_with_gaussian_filter(data, sigma)
