import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import glob
import argparse

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

from timesurl import TimesURL

def test_temporal_dependency_learning(model, test_data):
    """测试模型是否学习到时间依赖关系"""
    
    print("=== 时间依赖性学习测试 ===")
    
    model.eval()
    
    # 1. 时间顺序敏感性测试
    print("\n1. 时间顺序敏感性测试...")
    
    # 选择一个测试样本
    sample_idx = 0
    original_sample = test_data[sample_idx:sample_idx+1]  # [1, 500, 37]
    
    # 创建时间打乱版本
    shuffled_sample = original_sample.copy()
    time_indices = np.arange(original_sample.shape[1])
    np.random.shuffle(time_indices)
    shuffled_sample[:, :, :-1] = original_sample[:, time_indices, :-1]  # 保持时间特征不变
    
    # 获取表征
    try:
        with torch.no_grad():
            original_repr = model.encode(original_sample)
            shuffled_repr = model.encode(shuffled_sample)
        
        # 计算表征差异
        repr_diff = np.mean(np.abs(original_repr - shuffled_repr))
        print(f"  原始 vs 时间打乱的表征差异: {repr_diff:.6f}")
        
        if repr_diff < 0.001:
            print("  ⚠️ 警告: 表征差异很小，模型可能没有学习到时间顺序信息")
        else:
            print("  ✅ 模型对时间顺序敏感")
            
    except Exception as e:
        print(f"  时间顺序测试失败: {e}")
        repr_diff = 0
    
    # 2. 长期依赖测试
    print("\n2. 长期依赖测试...")
    
    try:
        # 创建不同长度的截断版本
        lengths = [50, 100, 200, 300, 400, 500]
        representations = []
        
        for length in lengths:
            truncated_sample = original_sample[:, :length, :]
            with torch.no_grad():
                repr_truncated = model.encode(truncated_sample)
            representations.append(repr_truncated)
        
        # 计算表征随长度的变化
        repr_changes = []
        for i in range(1, len(representations)):
            change = np.mean(np.abs(representations[i] - representations[i-1]))
            repr_changes.append(change)
            print(f"  长度 {lengths[i-1]} -> {lengths[i]}: 表征变化 {change:.6f}")
        
        # 检查是否存在长期依赖
        long_term_std = np.std(repr_changes)
        if long_term_std < 0.001:
            print("  ⚠️ 警告: 表征变化很小，可能缺乏长期依赖学习")
        else:
            print("  ✅ 模型表现出长期依赖特性")
            
    except Exception as e:
        print(f"  长期依赖测试失败: {e}")
        long_term_std = 0
    
    # 3. 特征重要性测试
    print("\n3. 特征重要性测试...")
    
    try:
        feature_importance = []
        baseline_repr = model.encode(original_sample)
        
        for feat_idx in range(min(10, original_sample.shape[2] - 1)):  # 只测试前10个特征
            # 将某个特征置零
            modified_sample = original_sample.copy()
            modified_sample[:, :, feat_idx] = 0
            
            with torch.no_grad():
                modified_repr = model.encode(modified_sample)
            
            importance = np.mean(np.abs(baseline_repr - modified_repr))
            feature_importance.append(importance)
            
            if feat_idx < 5:  # 只打印前5个特征
                print(f"  特征 {feat_idx+1} 重要性: {importance:.6f}")
        
        # 检查特征重要性分布
        importance_std = np.std(feature_importance)
        print(f"  特征重要性标准差: {importance_std:.6f}")
        
        if importance_std < 0.001:
            print("  ⚠️ 警告: 所有特征重要性相似，可能存在学习问题")
        else:
            print("  ✅ 模型学习到了特征差异")
            
    except Exception as e:
        print(f"  特征重要性测试失败: {e}")
        importance_std = 0
    
    return {
        'temporal_sensitivity': repr_diff,
        'long_term_dependency': long_term_std,
        'feature_importance_std': importance_std
    }

def comprehensive_model_diagnosis(model_path=None, test_data_path=None):
    """综合模型诊断"""
    
    print("=== TimesURL 模型综合诊断 ===")
    
    # 如果没有提供路径，自动查找
    if model_path is None:
        model_pattern = "training/*/model.pkl"
        model_files = glob.glob(model_pattern)
        if model_files:
            model_path = max(model_files, key=os.path.getctime)
            print(f"自动找到模型: {model_path}")
        else:
            print("❌ 未找到模型文件")
            return None, None
    
    if test_data_path is None:
        # 尝试从模型路径推断数据集名称
        dataset_name = model_path.split('/')[-2].split('__')[0]
        test_data_path = f"src/dataset/{dataset_name}/{dataset_name}_test.npy"
        print(f"推断测试数据路径: {test_data_path}")
    
    # 加载模型和数据
    try:
        model = TimesURL.load(model_path)
        test_data = np.load(test_data_path)
        
        print(f"✅ 模型加载成功: {model_path}")
        print(f"✅ 测试数据形状: {test_data.shape}")
        
        # 执行各项测试
        temporal_results = test_temporal_dependency_learning(model, test_data)
        
        # 综合评估
        print("\n=== 综合诊断结果 ===")
        
        issues = []
        if temporal_results['temporal_sensitivity'] < 0.001:
            issues.append("时间顺序敏感性不足")
        if temporal_results['long_term_dependency'] < 0.001:
            issues.append("长期依赖学习不足")
        if temporal_results['feature_importance_std'] < 0.001:
            issues.append("特征学习不充分")
        
        if issues:
            print("⚠️ 发现的问题:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("✅ 模型学习状态良好")
        
        return temporal_results, None
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_path', type=str, help='模型文件路径')
    parser.add_argument('--test_data_path', type=str, help='测试数据路径')
    args = parser.parse_args()
    
    comprehensive_model_diagnosis(args.model_path, args.test_data_path)
