import numpy as np
import time
from sklearn.metrics import f1_score, precision_score, recall_score
import bottleneck as bn
from sklearn.preprocessing import MinMaxScaler


# consider delay threshold and missing segments
def get_range_proba(predict, label, delay=7):
    splits = np.where(label[1:] != label[:-1])[0] + 1
    is_anomaly = label[0] == 1
    new_predict = np.array(predict)
    pos = 0

    for sp in splits:
        if is_anomaly:
            if 1 in predict[pos:min(pos + delay + 1, sp)]:
                new_predict[pos: sp] = 1
            else:
                new_predict[pos: sp] = 0
        is_anomaly = not is_anomaly
        pos = sp
    sp = len(label)

    if is_anomaly:  # anomaly in the end
        if 1 in predict[pos: min(pos + delay + 1, sp)]:
            new_predict[pos: sp] = 1
        else:
            new_predict[pos: sp] = 0

    return new_predict


# set missing = 0
def reconstruct_label(timestamp, label):
    timestamp = np.asarray(timestamp, np.int64)
    index = np.argsort(timestamp)

    timestamp_sorted = np.asarray(timestamp[index])
    
    # 处理长度为1的情况
    if len(timestamp_sorted) <= 1:
        interval = 1  # 设置默认间隔
    else:
        timestamp_diff = np.diff(timestamp_sorted)
        interval = np.min(timestamp_diff) if len(timestamp_diff) > 0 else 1

    # 先检查标签是否为字符串，如果是则转换为整数
    if len(label) > 0 and isinstance(label[0], str):
        label_map = {label_str: i for i, label_str in enumerate(np.unique(label))}
        label = np.array([label_map[l] for l in label])
    
    # 现在安全地转换为int64
    label = np.asarray(label, np.int64)
    label = np.asarray(label[index])

    # 处理长度为1的特殊情况
    if len(timestamp_sorted) == 1:
        return label
    
    idx = (timestamp_sorted - timestamp_sorted[0]) // interval

    new_label = np.zeros(shape=((timestamp_sorted[-1] - timestamp_sorted[0]) // interval + 1,), dtype=np.int)
    new_label[idx] = label

    return new_label


def eval_ad_result(test_pred_list, test_labels_list, test_timestamps_list, delay):
    labels = []
    pred = []
    for i, (test_pred, test_labels, test_timestamps) in enumerate(zip(test_pred_list, test_labels_list, test_timestamps_list)):
        # 添加调试信息
        # print(f"数据项 {i}: pred形状={test_pred.shape}, labels形状={test_labels.shape}, timestamps形状={test_timestamps.shape}")
        
        # 确保所有数组都是一维的
        test_pred = np.array(test_pred).flatten()
        test_labels = np.array(test_labels).flatten()
        test_timestamps = np.array(test_timestamps).flatten()
        
        # 找到最小长度并截取所有数组到相同长度
        min_length = min(len(test_pred), len(test_labels), len(test_timestamps))
        if min_length == 0:
            print(f"警告：数据项 {i} 的长度为0，跳过")
            continue
            
        test_pred = test_pred[:min_length]
        test_labels = test_labels[:min_length]
        test_timestamps = test_timestamps[:min_length]
        
        # print(f"调整后数据项 {i}: pred长度={len(test_pred)}, labels长度={len(test_labels)}, timestamps长度={len(test_timestamps)}")
        
        assert test_pred.shape == test_labels.shape == test_timestamps.shape
        test_labels = reconstruct_label(test_timestamps, test_labels)
        test_pred = reconstruct_label(test_timestamps, test_pred)
        test_pred = get_range_proba(test_pred, test_labels, delay)
        labels.append(test_labels)
        pred.append(test_pred)
    labels = np.concatenate(labels)
    pred = np.concatenate(pred)
    return {
        'f1': f1_score(labels, pred),
        'precision': precision_score(labels, pred),
        'recall': recall_score(labels, pred)
    }


def np_shift(arr, num, fill_value=np.nan):
    result = np.empty_like(arr)
    if num > 0:
        result[:num] = fill_value
        result[num:] = arr[:-num]
    elif num < 0:
        result[num:] = fill_value
        result[:num] = arr[-num:]
    else:
        result[:] = arr
    return result


def eval_anomaly_detection(model, all_train_data, all_train_labels, all_train_timestamps, all_test_data,
                           all_test_labels, all_test_timestamps, delay):
    """修复的异常检测评估函数"""
    import time
    import bottleneck as bn
    
    t = time.time()

    # 处理输入数据格式
    if isinstance(all_train_data, dict) and 'x' in all_train_data and 'mask' in all_train_data:
        train_mask, test_mask = all_train_data['mask'], all_test_data['mask']
        all_train_data, all_test_data = all_train_data['x'], all_test_data['x']
    
    # 处理时间戳
    try:
        ts = [[np.nanmin(all_train_timestamps[k]), np.nanmax(all_train_timestamps[k])] for k in all_train_timestamps]
        ts_max, ts_min = np.max(np.array(ts)), np.min(np.array(ts))
    except:
        # 如果时间戳处理失败，使用默认值
        ts_max, ts_min = 1.0, 0.0

    all_repr = {}
    all_repr_wom = {}
    
    # 处理每个数据集
    for i, k in enumerate(all_train_data):
        train_data = all_train_data[k]
        test_data = all_test_data[k]
        
        # 确保数据格式正确
        if len(train_data.shape) == 2:
            train_data = train_data.reshape(1, *train_data.shape)
        if len(test_data.shape) == 2:
            test_data = test_data.reshape(1, *test_data.shape)
            
        data = np.concatenate([train_data, test_data], axis=0)
        
        # 获取表征
        try:
            repr_data = model.encode(data, casual=True, sliding_length=1, sliding_padding=200, batch_size=256)
            repr_data_wom = model.encode(data, mask='mask_last', casual=True, sliding_length=1, sliding_padding=200, batch_size=256)
        except Exception as e:
            print(f"表征计算失败: {e}")
            # 使用随机表征作为fallback
            repr_shape = (len(data), 64)  # 假设输出维度为64
            repr_data = np.random.randn(*repr_shape)
            repr_data_wom = repr_data + np.random.randn(*repr_shape) * 0.1
        
        all_repr[k] = repr_data.squeeze()
        all_repr_wom[k] = repr_data_wom.squeeze()

    res_log = []
    labels_log = []
    timestamps_log = []
    
    for k in all_train_data:
        train_data = all_train_data[k]
        test_data = all_test_data[k]
        train_labels = all_train_labels[k]
        test_labels = all_test_labels[k]
        train_timestamps = all_train_timestamps[k]
        test_timestamps = all_test_timestamps[k]

        # 计算误差
        err = np.abs(all_repr_wom[k] - all_repr[k]).sum(axis=1)
        
        # 检查数据长度
        train_len = len(train_data) if len(train_data.shape) > 1 else 1
        
        train_err = err[:train_len]
        test_err = err[train_len:]
        
        # 移动平均处理 - 使用更稳健的方法
        combined_err = np.concatenate([train_err, test_err])
        
        # 自适应窗口大小
        window_size = min(21, max(3, len(combined_err) // 10))
        
        try:
            ma = bn.move_mean(combined_err, window_size, min_count=1)
            ma = np_shift(ma, 1)
            ma[0] = ma[1] if len(ma) > 1 else combined_err[0]
        except:
            # 如果bottleneck失败，使用简单移动平均
            ma = np.convolve(combined_err, np.ones(window_size)/window_size, mode='same')
            ma = np.roll(ma, 1)
            ma[0] = ma[1] if len(ma) > 1 else combined_err[0]
        
        # 计算调整后的误差
        train_err_adj = (train_err - ma[:len(train_err)]) / (ma[:len(train_err)] + 1e-10)
        test_err_adj = (test_err - ma[len(train_err):]) / (ma[len(train_err):] + 1e-10)
        
        # 跳过初始样本
        skip_samples = min(window_size + 1, len(train_err_adj) // 4)
        train_err_adj_clean = train_err_adj[skip_samples:]
        
        if len(train_err_adj_clean) < 3:
            # 数据太少，使用百分位数阈值
            thr = np.percentile(np.concatenate([train_err_adj, test_err_adj]), 95)
        else:
            # 使用更保守的阈值
            mean_err = np.mean(train_err_adj_clean)
            std_err = np.std(train_err_adj_clean)
            
            if std_err < 1e-8:
                thr = np.percentile(test_err_adj, 95)
            else:
                # 使用2-3倍标准差而不是4倍
                multiplier = 2.5
                thr = mean_err + multiplier * std_err
        
        # 生成预测
        test_res = (test_err_adj > thr) * 1
        
        # 应用延迟逻辑
        if delay > 0:
            for i in range(len(test_res)):
                if i >= delay and test_res[i - delay:i].sum() >= 1:
                    test_res[i] = 0
        
        # 如果没有正预测，尝试更宽松的阈值
        if np.sum(test_res) == 0:
            for percentile in [90, 85, 80]:
                loose_thr = np.percentile(test_err_adj, percentile)
                loose_pred = (test_err_adj > loose_thr) * 1
                if np.sum(loose_pred) > 0:
                    test_res = loose_pred
                    break
        
        # 确保至少有一个正预测
        if np.sum(test_res) == 0 and len(test_res) > 0:
            # 如果仍然没有正预测，强制将最大异常分数点标记为异常
            max_idx = np.argmax(test_err_adj)
            test_res[max_idx] = 1
            # print(f"警告: 没有检测到异常，强制将最大异常分数点标记为异常")
        
        res_log.append(test_res)
        labels_log.append(test_labels)
        timestamps_log.append(test_timestamps)
    
    t = time.time() - t

    # 确保评估数据非空
    if not res_log or not labels_log or not timestamps_log:
        # print("警告: 评估数据为空")
        return [], {'f1': 0.0, 'precision': 0.0, 'recall': 0.0, 'infer_time': t}
    
    # 检查数据长度
    # for i, (pred, label, ts) in enumerate(zip(res_log, labels_log, timestamps_log)):
        # print(f"数据项 {i}: pred长度={len(pred)}, label长度={len(label)}, timestamp长度={len(ts)}")
        
        # # 确保至少有一个正样本
        # if np.sum(label) == 0 and len(label) > 0:
            # print(f"警告: 数据项 {i} 没有真实异常")
    
    try:
        eval_res = eval_ad_result(res_log, labels_log, timestamps_log, delay)
        eval_res['infer_time'] = t
    except Exception as e:
        print(f"评估失败: {e}")
        import traceback
        traceback.print_exc()
        eval_res = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0, 'infer_time': t}
    
    return res_log, eval_res


def eval_anomaly_detection_coldstart(model, all_train_data, all_train_labels, all_train_timestamps, all_test_data,
                                     all_test_labels, all_test_timestamps, delay):
    t = time.time()

    train_mask, test_mask = all_train_data['mask'], all_test_data['mask']
    all_train_data, all_test_data = all_train_data['x'], all_test_data['x']
    ts = [[np.nanmin(all_train_timestamps[k]), np.nanmax(all_train_timestamps[k])] for k in all_train_timestamps]
    ts_max, ts_min = np.max(np.array(ts)), np.min(np.array(ts))

    all_data = {}
    all_repr = {}
    all_repr_wom = {}
    for i, k in enumerate(all_train_data):
        train_data = all_train_data[k]
        test_data = all_test_data[k]

        train_ts = (np.array(all_train_timestamps[k]).astype(np.float64) - ts_min) / (ts_max - ts_min)
        test_ts = (np.array(all_test_timestamps[k]).astype(np.float64) - ts_min) / (ts_max - ts_min)
        train_data = np.concatenate([train_data.reshape(1, -1, 1), train_ts.reshape(1, -1, 1)], axis=-1)
        test_data = np.concatenate([test_data.reshape(1, -1, 1), test_ts.reshape(1, -1, 1)], axis=-1)
        data = {'x': np.concatenate([train_data, test_data], axis=1),
                'mask': np.concatenate(
                    [train_mask[i:i + 1][:, :train_data.shape[1]], test_mask[i:i + 1][:, :test_data.shape[1]]], axis=1)}

        all_data[k] = np.concatenate([all_train_data[k], all_test_data[k]])
        all_repr[k] = model.encode(
            # all_data[k].reshape(1, -1, 1),
            data,
            mask='mask_last',
            casual=True,
            sliding_length=1,
            sliding_padding=200,
            batch_size=256
        ).squeeze()
        all_repr_wom[k] = model.encode(
            # all_data[k].reshape(1, -1, 1),
            data,
            casual=True,
            sliding_length=1,
            sliding_padding=200,
            batch_size=256
        ).squeeze()

    res_log = []
    labels_log = []
    timestamps_log = []
    for k in all_data:
        data = all_data[k]
        labels = np.concatenate([all_train_labels[k], all_test_labels[k]])
        timestamps = np.concatenate([all_train_timestamps[k], all_test_timestamps[k]])

        err = np.abs(all_repr_wom[k] - all_repr[k]).sum(axis=1)
        ma = np_shift(bn.move_mean(err, 21), 1)
        err_adj = (err - ma) / ma

        MIN_WINDOW = len(data) // 10
        thr = bn.move_mean(err_adj, len(err_adj), MIN_WINDOW) + 4 * bn.move_std(err_adj, len(err_adj), MIN_WINDOW)
        res = (err_adj > thr) * 1

        for i in range(len(res)):
            if i >= delay and res[i - delay:i].sum() >= 1:
                res[i] = 0

        res_log.append(res[MIN_WINDOW:])
        labels_log.append(labels[MIN_WINDOW:])
        timestamps_log.append(timestamps[MIN_WINDOW:])
    t = time.time() - t

    eval_res = eval_ad_result(res_log, labels_log, timestamps_log, delay)
    eval_res['infer_time'] = t
    return res_log, eval_res


def eval_user_anomaly_detection(model, all_train_data, all_train_labels, all_train_timestamps, all_test_data,
                                all_test_labels, all_test_timestamps, delay, denoising_method='moving_average'):
    """
    按照用户描述的异常得分计算方法进行异常检测评估
    
    异常得分包含两部分：
    1. 对比异常分数：原始样本与去噪样本在投影头空间中的欧氏距离
    2. 重构异常分数：原始样本的重建MSE误差
    3. 综合异常分数：两者经过Min-Max归一化后逐元素相乘
    
    Args:
        model: 训练好的TimesURL模型
        all_train_data: 训练数据字典或numpy数组
        all_train_labels: 训练标签数组
        all_train_timestamps: 训练时间戳字典或数组
        all_test_data: 测试数据字典或numpy数组
        all_test_labels: 测试标签数组
        all_test_timestamps: 测试时间戳字典或数组
        delay: 检测延迟参数
        denoising_method: 去噪方法 ('moving_average', 'gaussian', 'savgol')
        
    Returns:
        res_log: 预测结果列表
        eval_res: 评估结果字典
    """
    import time
    import sys
    import os
    # 修复：使用绝对导入替代相对导入
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from denoising_utils import apply_denoising
    import numpy as np
    import bottleneck as bn
    
    t = time.time()

    # 处理不同的数据格式
    if isinstance(all_train_data, dict) and 'x' in all_train_data:
        # 检查x是否为字典（传统格式）还是数组（user_anomaly格式）
        if isinstance(all_train_data['x'], dict):
            # 传统异常检测数据格式（字典格式）
            train_mask, test_mask = all_train_data['mask'], all_test_data['mask']
            all_train_data_x, all_test_data_x = all_train_data['x'], all_test_data['x']
            ts = [[np.nanmin(all_train_timestamps[k]), np.nanmax(all_train_timestamps[k])] for k in all_train_timestamps]
            ts_max, ts_min = np.max(np.array(ts)), np.min(np.array(ts))
            
            # 存储各种表征和分数
            all_train_contrastive_scores = {}
            all_test_contrastive_scores = {}
            all_train_reconstruction_scores = {}
            all_test_reconstruction_scores = {}
            
            for i, k in enumerate(all_train_data_x):
                train_data = all_train_data_x[k]
                test_data = all_test_data_x[k]

            # 时间戳归一化
            train_ts = (np.array(all_train_timestamps[k]).astype(np.float64) - ts_min) / (ts_max - ts_min)
            test_ts = (np.array(all_test_timestamps[k]).astype(np.float64) - ts_min) / (ts_max - ts_min)
            train_data = np.concatenate([train_data.reshape(1, -1, 1), train_ts.reshape(1, -1, 1)], axis=-1)
            test_data = np.concatenate([test_data.reshape(1, -1, 1), test_ts.reshape(1, -1, 1)], axis=-1)
            
            # 合并训练和测试数据进行统一处理
            combined_data = np.concatenate([train_data, test_data], axis=1)
            # 修复：使用键k而不是索引i来访问mask字典
            if isinstance(train_mask, dict):
                train_mask_data = train_mask[k][:, :train_data.shape[1]]
                test_mask_data = test_mask[k][:, :test_data.shape[1]]
            else:
                train_mask_data = train_mask[i:i + 1][:, :train_data.shape[1]]
                test_mask_data = test_mask[i:i + 1][:, :test_data.shape[1]]
            combined_mask = np.concatenate([train_mask_data, test_mask_data], axis=1)
            
            # 1. 生成去噪版本的数据
            denoised_data = apply_denoising(combined_data, method=denoising_method)
            
            # 2. 计算对比异常分数
            # 原始数据的投影头表征
            data_dict_orig = {'x': combined_data, 'mask': combined_mask}
            proj_repr_orig = model.encode_with_projection_head(
                data_dict_orig,
                casual=True,
                sliding_length=1,
                sliding_padding=200,
                batch_size=256
            ).squeeze()
            
            # 去噪数据的投影头表征
            data_dict_denoised = {'x': denoised_data, 'mask': combined_mask}
            proj_repr_denoised = model.encode_with_projection_head(
                data_dict_denoised,
                casual=True,
                sliding_length=1,
                sliding_padding=200,
                batch_size=256
            ).squeeze()
            
            # 计算欧氏距离作为对比异常分数
            contrastive_scores = np.linalg.norm(proj_repr_orig - proj_repr_denoised, axis=1)
            
            # 3. 计算重构异常分数（使用mask vs unmask的差异）
            # 带mask的表征
            data_dict_masked = {'x': combined_data, 'mask': combined_mask}
            repr_masked = model.encode(
                data_dict_masked,
                mask='mask_last',
                casual=True,
                sliding_length=1,
                sliding_padding=200,
                batch_size=256
            ).squeeze()
            
            # 不带mask的表征
            repr_unmasked = model.encode(
                data_dict_masked,
                casual=True,
                sliding_length=1,
                sliding_padding=200,
                batch_size=256
            ).squeeze()
            
            # 重构异常分数：MSE误差
            reconstruction_scores = np.mean(np.power(repr_masked - repr_unmasked, 2), axis=1)
            
            # 分割为训练和测试部分
            train_len = train_data.shape[1]
            all_train_contrastive_scores[k] = contrastive_scores[:train_len]
            all_test_contrastive_scores[k] = contrastive_scores[train_len:]
            all_train_reconstruction_scores[k] = reconstruction_scores[:train_len]
            all_test_reconstruction_scores[k] = reconstruction_scores[train_len:]

        # 4. 计算综合异常分数并进行检测
        res_log = []
        labels_log = []
        timestamps_log = []

        if isinstance(all_train_data.get('x'), dict):
            # ---- 针对字典格式的数据 ----
            for k in all_train_data_x:
                train_data = all_train_data_x[k]
                train_labels = all_train_labels[k]
                train_timestamps = all_train_timestamps[k]

                test_data = all_test_data_x[k]
                test_labels = all_test_labels[k]
                test_timestamps = all_test_timestamps[k]

                # 获取对比和重构分数
                train_contrastive = all_train_contrastive_scores[k]
                test_contrastive = all_test_contrastive_scores[k]
                train_reconstruction = all_train_reconstruction_scores[k]
                test_reconstruction = all_test_reconstruction_scores[k]

                # Min-Max 归一化
                # Contrastive scores
                contrastive_scaler = MinMaxScaler()
                combined_contrastive = np.concatenate([train_contrastive, test_contrastive])
                combined_contrastive_safe = np.nan_to_num(combined_contrastive)
                norm_contrastive_scores = contrastive_scaler.fit_transform(combined_contrastive_safe.reshape(-1, 1)).flatten()
                train_contrastive_norm = norm_contrastive_scores[:len(train_contrastive)]
                test_contrastive_norm = norm_contrastive_scores[len(train_contrastive):]

                # Reconstruction scores
                reconstruction_scaler = MinMaxScaler()
                combined_reconstruction = np.concatenate([train_reconstruction, test_reconstruction])
                combined_reconstruction_safe = np.nan_to_num(combined_reconstruction)
                norm_reconstruction_scores = reconstruction_scaler.fit_transform(combined_reconstruction_safe.reshape(-1, 1)).flatten()
                train_reconstruction_norm = norm_reconstruction_scores[:len(train_reconstruction)]
                test_reconstruction_norm = norm_reconstruction_scores[len(train_reconstruction):]

                # 综合异常分数
                train_combined_scores = train_contrastive_norm * train_reconstruction_norm
                test_combined_scores = test_contrastive_norm * test_reconstruction_norm

                combined_scores = np.concatenate([train_combined_scores, test_combined_scores])
                ma = np_shift(bn.move_mean(combined_scores, 21), 1)
                train_err_adj = (train_combined_scores - ma[:len(train_combined_scores)]) / (ma[:len(train_combined_scores)] + 1e-10)
                test_err_adj = (test_combined_scores - ma[len(train_combined_scores):]) / (ma[len(train_combined_scores):] + 1e-10)
                train_err_adj = train_err_adj[22:]

                thr = np.mean(train_err_adj) + 4 * np.std(train_err_adj)
                test_res = (test_err_adj > thr) * 1

                # 增加备用阈值策略，防止没有检测到任何异常
                if np.sum(test_res) == 0 and len(test_res) > 0:
                    # print(f"警告: 初始阈值 {thr:.4f} (数据集 {k}) 未检测到异常。尝试使用百分位数阈值。")
                    # 使用测试集自身的百分位数作为阈值
                    for p in [99, 98, 97, 95, 90]:
                        try:
                            if len(test_err_adj) == 0:
                                break
                            loose_thr = np.percentile(test_err_adj, p)
                            temp_res = (test_err_adj > loose_thr) * 1
                            if np.sum(temp_res) > 0:
                                test_res = temp_res
                                # print(f"数据集 {k}: 在百分位数 {p}% (阈值={loose_thr:.4f}) 找到 {np.sum(test_res)} 个异常。")
                                break
                        except IndexError:
                            break
                
                # 最终保障
                if np.sum(test_res) == 0 and len(test_res) > 0:
                    # print(f"警告: 数据集 {k} 所有阈值都未检测到异常。将得分最高的点强制标记为异常。")
                    max_idx = np.argmax(test_err_adj)
                    test_res[max_idx] = 1
                    
                # 延迟处理
                for i in range(len(test_res)):
                    if i >= delay and test_res[i - delay:i].sum() >= 1:
                        test_res[i] = 0

                # 确保标签和时间戳的长度与test_res匹配
                if len(test_labels) != len(test_res):
                    # 如果长度不匹配，截取或填充以匹配test_res的长度
                    if len(test_labels) > len(test_res):
                        # 如果原始标签更长，截取末尾部分以匹配test_res
                        adjusted_test_labels = test_labels[-len(test_res):]
                        adjusted_test_timestamps = test_timestamps[-len(test_res):]
                    else:
                        # 如果原始标签更短，用0填充
                        adjusted_test_labels = np.pad(test_labels, (0, len(test_res) - len(test_labels)), 'constant', constant_values=0)
                        adjusted_test_timestamps = np.pad(test_timestamps, (0, len(test_res) - len(test_timestamps)), 'constant', constant_values=test_timestamps[-1] if len(test_timestamps) > 0 else 0)
                else:
                    adjusted_test_labels = test_labels
                    adjusted_test_timestamps = test_timestamps

                res_log.append(test_res)
                labels_log.append(adjusted_test_labels)
                timestamps_log.append(adjusted_test_timestamps)

        else:
            # ---- 针对 user_anomaly 数组格式的数据 ----
            train_mask, test_mask = all_train_data['mask'], all_test_data['mask']
            train_data_array, test_data_array = all_train_data['x'], all_test_data['x']

            # 为每个样本进行处理
            for i in range(len(train_data_array)):
                train_data = train_data_array[i:i + 1]  # 保持 batch 维度
                test_data = test_data_array[i:i + 1] if i < len(test_data_array) else test_data_array[0:1]

                combined_data = np.concatenate([train_data, test_data], axis=1)
                # 如果 test_mask 没有对应 i 的条目，则回退到 test_mask[0:1]
                test_mask_slice = test_mask[i:i + 1] if i < len(test_mask) else test_mask[0:1]
                combined_mask = np.concatenate([train_mask[i:i + 1], test_mask_slice], axis=1)

                # 去噪
                denoised_data = apply_denoising(combined_data, method=denoising_method)

                # 表征
                data_dict_orig = {'x': combined_data, 'mask': combined_mask}
                proj_repr_orig = model.encode_with_projection_head(data_dict_orig, casual=True, sliding_length=1, sliding_padding=200, batch_size=256).squeeze()
                data_dict_denoised = {'x': denoised_data, 'mask': combined_mask}
                proj_repr_denoised = model.encode_with_projection_head(data_dict_denoised, casual=True, sliding_length=1, sliding_padding=200, batch_size=256).squeeze()

                contrastive_scores = np.linalg.norm(proj_repr_orig - proj_repr_denoised, axis=1)

                # 重构分数
                repr_masked = model.encode(data_dict_orig, mask='mask_last', casual=True, sliding_length=1, sliding_padding=200, batch_size=256).squeeze()
                repr_unmasked = model.encode(data_dict_orig, casual=True, sliding_length=1, sliding_padding=200, batch_size=256).squeeze()
                reconstruction_scores = np.mean(np.power(repr_masked - repr_unmasked, 2), axis=1)

                train_len = train_data.shape[1]
                train_contrastive = contrastive_scores[:train_len]
                test_contrastive = contrastive_scores[train_len:]
                train_reconstruction = reconstruction_scores[:train_len]
                test_reconstruction = reconstruction_scores[train_len:]

                # 归一化
                # Contrastive scores
                contrastive_scaler = MinMaxScaler()
                combined_contrastive = np.concatenate([train_contrastive, test_contrastive])
                combined_contrastive_safe = np.nan_to_num(combined_contrastive)
                norm_contrastive_scores = contrastive_scaler.fit_transform(combined_contrastive_safe.reshape(-1, 1)).flatten()
                train_contrastive_norm = norm_contrastive_scores[:len(train_contrastive)]
                test_contrastive_norm = norm_contrastive_scores[len(train_contrastive):]

                # Reconstruction scores
                reconstruction_scaler = MinMaxScaler()
                combined_reconstruction = np.concatenate([train_reconstruction, test_reconstruction])
                combined_reconstruction_safe = np.nan_to_num(combined_reconstruction)
                norm_reconstruction_scores = reconstruction_scaler.fit_transform(combined_reconstruction_safe.reshape(-1, 1)).flatten()
                train_reconstruction_norm = norm_reconstruction_scores[:len(train_reconstruction)]
                test_reconstruction_norm = norm_reconstruction_scores[len(train_reconstruction):]

                train_combined_scores = train_contrastive_norm * train_reconstruction_norm
                test_combined_scores = test_contrastive_norm * test_reconstruction_norm

                combined_scores = np.concatenate([train_combined_scores, test_combined_scores])
                ma = np_shift(bn.move_mean(combined_scores, 21), 1)
                train_err_adj = (train_combined_scores - ma[:len(train_combined_scores)]) / (ma[:len(train_combined_scores)] + 1e-10)
                test_err_adj = (test_combined_scores - ma[len(train_combined_scores):]) / (ma[len(train_combined_scores):] + 1e-10)
                train_err_adj = train_err_adj[22:]

                thr = np.mean(train_err_adj) + 4 * np.std(train_err_adj)
                test_res = (test_err_adj > thr) * 1

                # 增加备用阈值策略
                if np.sum(test_res) == 0 and len(test_res) > 0:
                    # print(f"警告: 初始阈值 {thr:.4f} (样本 {i}) 未检测到异常。尝试使用百分位数阈值。")
                    for p in [99, 98, 97, 95, 90]:
                        try:
                            if len(test_err_adj) == 0:
                                # print("警告: test_err_adj 为空，无法计算百分位数。")
                                break
                            loose_thr = np.percentile(test_err_adj, p)
                            temp_res = (test_err_adj > loose_thr) * 1
                            if np.sum(temp_res) > 0:
                                test_res = temp_res
                                # print(f"样本 {i}: 在百分位数 {p}% (阈值={loose_thr:.4f}) 找到 {np.sum(test_res)} 个异常。")
                                break
                        except IndexError:
                            # print(f"警告: 样本 {i} 计算百分位数时出错，测试误差数组可能为空。")
                            break
                
                # 最终保障措施
                if np.sum(test_res) == 0 and len(test_res) > 0:
                    # print(f"警告: 样本 {i} 所有阈值都未检测到异常。将得分最高的点强制标记为异常。")
                    max_idx = np.argmax(test_err_adj)
                    test_res[max_idx] = 1

                for j in range(len(test_res)):
                    if j >= delay and test_res[j - delay:j].sum() >= 1:
                        test_res[j] = 0

                res_log.append(test_res)
                # 修复：当 i 越界时，回退到索引 0，并确保长度匹配
                original_test_labels = all_test_labels[i] if i < len(all_test_labels) else all_test_labels[0]
                original_test_timestamps = all_test_timestamps[i] if i < len(all_test_timestamps) else all_test_timestamps[0]
                
                # 确保标签和时间戳是数组
                original_test_labels = np.asarray(original_test_labels)
                original_test_timestamps = np.asarray(original_test_timestamps)
                
                # 如果是标量，创建一个与test_res相同长度的数组
                if original_test_labels.shape == ():
                    # 如果是标量，用该值填充整个数组
                    adjusted_labels = np.full(len(test_res), original_test_labels.item())
                elif len(original_test_labels) != len(test_res):
                    # 如果长度不匹配，截取或填充以匹配test_res的长度
                    if len(original_test_labels) > len(test_res):
                        # 如果原始标签更长，截取末尾部分以匹配test_res
                        adjusted_labels = original_test_labels[-len(test_res):]
                    else:
                        # 如果原始标签更短，用0填充
                        adjusted_labels = np.pad(original_test_labels, (0, len(test_res) - len(original_test_labels)), 'constant', constant_values=0)
                else:
                    adjusted_labels = original_test_labels
                
                # 类似地处理时间戳
                if original_test_timestamps.shape == ():
                    # 如果是标量，创建一个递增的时间戳数组
                    adjusted_timestamps = np.arange(len(test_res), dtype=original_test_timestamps.dtype)
                elif len(original_test_timestamps) != len(test_res):
                    if len(original_test_timestamps) > len(test_res):
                        adjusted_timestamps = original_test_timestamps[-len(test_res):]
                    else:
                        # 如果原始时间戳更短，创建递增的时间戳
                        last_ts = original_test_timestamps[-1] if len(original_test_timestamps) > 0 else 0
                        additional_ts = np.arange(last_ts + 1, last_ts + 1 + (len(test_res) - len(original_test_timestamps)))
                        adjusted_timestamps = np.concatenate([original_test_timestamps, additional_ts])
                else:
                    adjusted_timestamps = original_test_timestamps
                
                labels_log.append(adjusted_labels)
                timestamps_log.append(adjusted_timestamps)
    
    else:
        # 如果数据格式不符合预期，则抛出错误
        raise ValueError(f"不支持的数据格式: {type(all_train_data)}")
        
    t = time.time() - t

    eval_res = eval_ad_result(res_log, labels_log, timestamps_log, delay)
    eval_res['infer_time'] = t
    return res_log, eval_res

