#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试门控网络自适应去噪系统
"""

import sys
import os
import numpy as np
import torch
import torch.nn.functional as F

# 添加src目录到Python路径
sys.path.append('src')

try:
    from models.learnable_gating import (
        AdaptiveDenoisingModule, 
        adaptive_denoising_training_step,
        print_gating_statistics,
        integrate_gating_with_timesurl
    )
    print("✅ 成功导入门控网络模块")
except ImportError as e:
    print(f"❌ 导入门控网络模块失败: {e}")
    sys.exit(1)

def test_gating_network_basic():
    """测试门控网络基础功能"""
    print("\n🔧 === 测试门控网络基础功能 ===")
    
    # 模拟数据
    batch_size, seq_len, features = 4, 500, 36
    x_batch = torch.randn(batch_size, seq_len, features)
    
    print(f"📊 测试数据形状: {x_batch.shape}")
    
    # 创建自适应去噪模块
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    adaptive_denoising = AdaptiveDenoisingModule(features, device=device)
    x_batch = x_batch.to(device)
    
    print(f"📱 使用设备: {device}")
    print(f"🛠️ 可用去噪器: {adaptive_denoising.denoiser_names}")
    
    # 训练模式测试
    print("\n🔄 训练模式测试 (Gumbel-Softmax软选择):")
    adaptive_denoising.train()
    x_denoised, gate_weights, gate_logits = adaptive_denoising(x_batch, training=True)
    
    print(f"   ✅ 输入形状: {x_batch.shape}")
    print(f"   ✅ 输出形状: {x_denoised.shape}")
    print(f"   ✅ 门控权重形状: {gate_weights.shape}")
    print(f"   ✅ 门控logits形状: {gate_logits.shape}")
    
    # 验证权重和为1
    weight_sums = gate_weights.sum(dim=1)
    print(f"   ✅ 权重和检查: {weight_sums.min().item():.3f} ~ {weight_sums.max().item():.3f} (应接近1.0)")
    
    # 推理模式测试
    print("\n⚡ 推理模式测试 (硬选择，高效):")
    adaptive_denoising.eval()
    with torch.no_grad():
        x_denoised_eval, gate_weights_eval, gate_logits_eval = adaptive_denoising(x_batch, training=False)
    
    print(f"   ✅ 输入形状: {x_batch.shape}")
    print(f"   ✅ 输出形状: {x_denoised_eval.shape}")
    
    # 验证硬选择（每行只有一个1）
    ones_per_row = gate_weights_eval.sum(dim=1)
    print(f"   ✅ 硬选择检查: 每样本选择数量 = {ones_per_row.unique().cpu().numpy()} (应为[1.0])")
    
    print_gating_statistics(gate_weights, adaptive_denoising.denoiser_names)
    print_gating_statistics(gate_weights_eval, adaptive_denoising.denoiser_names)
    
    return True

def test_training_step():
    """测试完整的训练步骤"""
    print("\n🚀 === 测试完整训练步骤 ===")
    
    # 模拟数据
    batch_size, seq_len, features = 2, 500, 36
    x_batch = torch.randn(batch_size, seq_len, features)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    adaptive_denoising = AdaptiveDenoisingModule(features, device=device)
    x_batch = x_batch.to(device)
    
    print(f"📊 测试数据形状: {x_batch.shape}")
    
    # 测试训练步骤
    print("\n🔄 执行训练步骤:")
    x_denoised, gate_weights = adaptive_denoising_training_step(
        x_batch, adaptive_denoising, training=True
    )
    
    print(f"✅ 去噪数据形状: {x_denoised.shape}")
    print(f"✅ 门控权重形状: {gate_weights.shape}")
    
    # 验证门控网络可以接收梯度（通过模拟DECL损失）
    print("\n🔍 检查通过DECL损失的梯度传播:")
    # 模拟DECL损失：使去噪数据与原数据相似
    decl_loss = F.mse_loss(x_denoised, x_batch)
    print(f"   模拟DECL损失: {decl_loss.item():.6f}")
    
    decl_loss.backward()
    
    # 检查门控网络是否有梯度
    has_gradient = False
    for name, param in adaptive_denoising.named_parameters():
        if param.grad is not None:
            grad_norm = param.grad.norm().item()
            print(f"   ✅ {name}: grad_norm = {grad_norm:.6f}")
            has_gradient = True
    
    if has_gradient:
        print("✅ 门控网络可以通过DECL损失获得梯度")
    else:
        print("❌ 门控网络没有梯度")
    
    return True

def test_with_real_data():
    """使用真实数据测试"""
    print("\n📈 === 使用真实数据测试 ===")
    
    try:
        import datautils
        print("✅ 成功导入datautils")
        
        # 加载ConstPos数据集
        train_data, train_labels, test_data, test_labels = datautils.load_user_anomaly('ConstPos', load_tp=True)
        print(f"✅ 成功加载ConstPos数据集")
        print(f"   训练数据形状: {train_data['x'].shape}")
        
        # 创建门控系统
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 模拟TimesURL模型（用于集成测试）
        class MockTimesURLModel:
            def __init__(self):
                self.device = device
        
        mock_model = MockTimesURLModel()
        adaptive_denoising = integrate_gating_with_timesurl(mock_model, train_data, device=device)
        
        # 取一小批数据测试
        x_sample = torch.tensor(train_data['x'][:2], dtype=torch.float32).to(device)  # 只取2个样本
        print(f"📊 样本数据形状: {x_sample.shape}")
        
        # 排除时间位置特征
        x_features_only = x_sample[..., :-1]  # 排除最后一维时间特征
        print(f"📊 特征数据形状 (排除时间): {x_features_only.shape}")
        
        # 测试门控去噪
        print("\n🎯 测试真实数据门控去噪:")
        x_denoised, gate_weights = adaptive_denoising_training_step(
            x_features_only, adaptive_denoising, training=True
        )
        
        print(f"✅ 去噪成功！")
        print(f"   输入形状: {x_features_only.shape}")
        print(f"   输出形状: {x_denoised.shape}")
        
        print_gating_statistics(gate_weights, adaptive_denoising.denoiser_names)
        
        return True
        
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 门控网络自适应去噪系统测试")
    print("=" * 50)
    
    tests = [
        ("基础功能测试", test_gating_network_basic),
        ("训练步骤测试", test_training_step),
        ("真实数据测试", test_with_real_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🚀 运行 {test_name}...")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 50)
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！门控网络系统工作正常。")
        print("\n📋 接下来可以:")
        print("  1. 运行完整的DECL训练: python run_user_anomaly.py ConstPos")
        print("  2. 观察门控网络的选择行为")
        print("  3. 比较与传统方法的性能差异")
    else:
        print("⚠️ 部分测试失败，请检查错误信息并修复。")

if __name__ == "__main__":
    main() 