import os
import glob

def run_complete_diagnosis():
    """运行完整的训练问题诊断"""
    
    print("=== TimesURL 训练问题诊断 ===\n")
    
    # 1. 基准测试
    print("1. 运行性能基准测试...")
    os.system("python benchmark_training_time.py")
    
    # 2. 找到最新的模型文件
    model_pattern = "training/ConstPos__test_run_*/model.pkl"
    model_files = glob.glob(model_pattern)
    
    if model_files:
        latest_model = max(model_files, key=os.path.getctime)
        print(f"\n2. 找到模型文件: {latest_model}")
        
        # 3. 运行模型诊断
        print("3. 运行模型学习效果诊断...")
        os.system(f"python test_temporal_learning.py --model_path {latest_model}")
        
    else:
        print("\n2. 未找到训练好的模型，请先运行训练")
    
    # 4. 数据质量检查
    print("\n4. 检查数据质量...")
    os.system("python analyze_dataset_quality.py")
    
    print("\n=== 诊断完成 ===")
    print("请查看上述输出，重点关注:")
    print("- 训练时间是否合理")
    print("- 模型是否学习到时间依赖")
    print("- 梯度和参数变化是否正常")
    print("- 数据质量是否足够")

if __name__ == "__main__":
    run_complete_diagnosis()