#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于门控网络和Gumbel-Softmax的自适应去噪系统
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
import concurrent.futures

# 修复导入路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from denoising_utils import (
    denoise_with_moving_average,
    denoise_with_gaussian_filter,
    denoise_with_savgol_filter,
    denoise_with_median_filter,
    denoise_with_wavelet
)

class GumbelhSoftmaxGatingNetwork(nn.Module):
    """基于Gumbel-Softmax的门控网络"""
    
    def __init__(self, input_dim, hidden_dim=64, num_denoisers=7):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_denoisers = num_denoisers
        
        # 特征提取网络 - 轻量级设计
        self.feature_extractor = nn.Sequential(
            # 1D卷积提取时间特征
            nn.Conv1d(input_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),  # 全局平均池化
            nn.Flatten(),
            
            # 全连接层
            nn.Linear(hidden_dim // 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
        )
        
        # 门控决策层
        self.gate_logits = nn.Linear(hidden_dim // 2, num_denoisers)
        
        # 温度退火参数（按论文描述）
        self.initial_temperature = 2.0
        self.final_temperature = 0.1
        self.current_epoch = 0
        
    def update_temperature(self, epoch, max_epochs):
        """
        指数退火策略更新温度
        初始温度2.0，逐步退火降低至0.1
        """
        self.current_epoch = epoch
        # 指数退火公式
        decay_rate = (self.final_temperature / self.initial_temperature) ** (1.0 / max_epochs)
        self.current_temperature = self.initial_temperature * (decay_rate ** epoch)
        self.current_temperature = max(self.current_temperature, self.final_temperature)
        
    def forward(self, x, epoch=0, max_epochs=100):
        """
        前向传播 - 按论文描述实现
        
        Args:
            x: 输入数据 [batch_size, seq_len, features]
            epoch: 当前训练轮次
            max_epochs: 总训练轮次
            
        Returns:
            selected_indices: 选中的去噪器索引 [batch_size]
            gumbel_weights: Gumbel-Softmax权重（用于反向传播）
            logits: 原始logits
        """
        # 更新温度
        self.update_temperature(epoch, max_epochs)
        
        # 转换为卷积输入格式 [batch, features, seq_len]
        x = x.transpose(1, 2)
        
        # 特征提取
        features = self.feature_extractor(x)
        
        # 计算logits
        logits = self.gate_logits(features)
        
        # Gumbel-Softmax采样 - 前向传播时明确选择一个去噪器
        gumbel_weights = F.gumbel_softmax(logits, tau=self.current_temperature, hard=True)
        
        # 获取选中的去噪器索引
        selected_indices = torch.argmax(gumbel_weights, dim=1)
        
        return selected_indices, gumbel_weights, logits

class AdaptiveDenoisingModule(nn.Module):
    """自适应去噪模块 - 按论文描述实现"""
    
    def __init__(self, input_dim, device='cuda'):
        super().__init__()
        self.device = device
        self.gating_network = GumbelhSoftmaxGatingNetwork(input_dim).to(device)
        
        # 添加投影头用于编码
        self.projection_head = nn.Linear(320, 320).to(device)  # 320是repr_dims
        
        # 定义去噪器配置
        self.denoiser_configs = [
            ('moving_average', {'window_size': 3}),
            ('moving_average', {'window_size': 5}),
            ('gaussian', {'sigma': 0.8}),
            ('gaussian', {'sigma': 1.2}),
            ('savgol', {'window_length': 5, 'polyorder': 2}),
            ('median', {'kernel_size': 3}),
            ('wavelet', {'wavelet': 'db4', 'level': 2})
        ]
        
        self.denoiser_names = [f"{config[0]}_{i}" for i, (config, _) in enumerate(self.denoiser_configs)]
        
    def apply_single_denoiser(self, data_np, method_name, params):
        """应用单个去噪器"""
        if method_name == 'moving_average':
            return denoise_with_moving_average(data_np, **params)
        elif method_name == 'gaussian':
            return denoise_with_gaussian_filter(data_np, **params)
        elif method_name == 'savgol':
            return denoise_with_savgol_filter(data_np, **params)
        elif method_name == 'median':
            return denoise_with_median_filter(data_np, **params)
        elif method_name == 'wavelet':
            return denoise_with_wavelet(data_np, **params)
        else:
            return data_np
    
    def forward(self, x, epoch=0, max_epochs=100):
        """
        前向传播 - 按论文描述实现
        论文：在前向传播时，通过一次类别采样，明确地选择一个最适合当前样本的去噪器
        
        Args:
            x: 输入数据 [batch_size, seq_len, features]
            epoch: 当前训练轮次
            max_epochs: 总训练轮次
            
        Returns:
            denoised_data: 去噪后的数据
            gumbel_weights: Gumbel-Softmax权重（用于反向传播梯度）
            gate_logits: 门控logits
        """
        batch_size = x.shape[0]
        
        # 1. 获取门控网络选择（明确选择一个去噪器）
        selected_indices, gumbel_weights, gate_logits = self.gating_network(x, epoch, max_epochs)
        
        # 2. 按论文描述：明确地选择一个去噪器并应用
        denoised_data = torch.zeros_like(x)
        x_np = x.detach().cpu().numpy()
        
        # 为每个批次样本应用其选中的去噪器
        for batch_idx in range(batch_size):
            selected_idx = selected_indices[batch_idx].item()
            method_name, params = self.denoiser_configs[selected_idx]
            
            try:
                # 只对当前样本应用选中的去噪器
                sample_data = x_np[batch_idx:batch_idx+1]
                denoised = self.apply_single_denoiser(sample_data, method_name, params)
                denoised_data[batch_idx] = torch.tensor(denoised[0], dtype=torch.float32, device=self.device)
            except Exception:
                # 如果失败，使用原始数据
                denoised_data[batch_idx] = x[batch_idx]
        
        return denoised_data, gumbel_weights, gate_logits

def integrate_gating_with_timesurl(timesurl_model, train_data, device='cuda'):
    """
    将门控网络集成到TimesURL模型中
    
    Args:
        timesurl_model: TimesURL模型实例
        train_data: 训练数据
        device: 设备
        
    Returns:
        adaptive_denoising_module: 训练好的自适应去噪模块
    """
    print("🚀 初始化基于Gumbel-Softmax的自适应去噪系统...")
    
    # 获取输入维度（排除时间位置特征）
    input_dim = train_data['x'].shape[2] - 1
    
    # 创建自适应去噪模块
    adaptive_denoising = AdaptiveDenoisingModule(input_dim, device=device)
    
    print(f"✅ 自适应去噪模块已创建")
    print(f"   - 输入维度: {input_dim}")
    print(f"   - 去噪器数量: {len(adaptive_denoising.denoiser_configs)}")
    print(f"   - 可用去噪器: {adaptive_denoising.denoiser_names}")
    
    return adaptive_denoising

# 算法1：基于门控网络的自适应去噪训练流程 - 按论文描述更新
def adaptive_denoising_training_step(x_batch, adaptive_denoising, epoch=0, max_epochs=100):
    """
    算法1：自适应去噪训练步骤 - 按论文描述实现
    
    论文描述：在前向传播时，通过一次类别采样，明确地选择一个最适合当前样本的去噪器。
    而在反向传播时，为此离散选择过程提供了一个连续、可微的"软"梯度近似。
    
    输入: x_batch - 原始时间序列批次
          epoch - 当前训练轮次（用于温度退火）
          max_epochs - 总训练轮次
    输出: x_denoised - 去噪后的数据, gumbel_weights - 门控权重
    
    步骤:
    1. 将x_batch输入门控网络，计算logits
    2. 使用Gumbel-Softmax采样，明确选择一个去噪器
    3. 应用选中的去噪器生成正样本视图
    4. 返回去噪结果和Gumbel-Softmax权重（用于梯度反传）
    
    注: 温度退火策略：初始2.0，逐步降至0.1，控制探索-利用权衡
    """
    # 步骤1-3: 自适应去噪（明确选择一个去噪器）
    x_denoised, gumbel_weights, gate_logits = adaptive_denoising(x_batch, epoch=epoch, max_epochs=max_epochs)
    
    # 不计算额外的门控损失，让门控网络通过DECL损失学习
    return x_denoised, gumbel_weights

def print_gating_statistics(gumbel_weights, denoiser_names, current_temperature=None):
    """打印门控选择统计信息"""
    # 计算每个去噪器的选择频率
    selection_probs = gumbel_weights.mean(dim=0).detach().cpu().numpy()
    
    print("📊 去噪器选择统计:")
    if current_temperature is not None:
        print(f"   🌡️ 当前温度: {current_temperature:.3f}")
    for i, (name, prob) in enumerate(zip(denoiser_names, selection_probs)):
        print(f"   {i}: {name:<20} {prob:.3f} {'█' * int(prob * 20)}")

# 使用示例 - 按论文描述更新
def demo_gating_system():
    """演示门控系统的使用 - 按论文描述"""
    print("🎯 门控网络自适应去噪系统演示（按论文描述）")
    
    # 模拟数据
    batch_size, seq_len, features = 8, 500, 36
    x_batch = torch.randn(batch_size, seq_len, features)
    
    # 创建自适应去噪模块
    adaptive_denoising = AdaptiveDenoisingModule(features)
    
    # 模拟训练过程中的温度退火
    max_epochs = 100
    print(f"\n🌡️ 温度退火演示:")
    for epoch in [0, 25, 50, 75, 99]:
        print(f"\n📅 Epoch {epoch}:")
        x_denoised, gumbel_weights = adaptive_denoising_training_step(
            x_batch, adaptive_denoising, epoch=epoch, max_epochs=max_epochs
        )
        current_temp = adaptive_denoising.gating_network.current_temperature
        print(f"   输入形状: {x_batch.shape}")
        print(f"   输出形状: {x_denoised.shape}")
        print(f"   当前温度: {current_temp:.3f}")
        print_gating_statistics(gumbel_weights, adaptive_denoising.denoiser_names, current_temp)

if __name__ == "__main__":
    demo_gating_system()
