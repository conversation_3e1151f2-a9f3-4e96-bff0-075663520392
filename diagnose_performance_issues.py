#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断TimesURL模型训练性能问题
分析训练速度、学习收敛、异常检测评估等关键指标
"""

import sys
import os
import numpy as np
import torch
import time
from types import SimpleNamespace

# 添加src目录到Python路径
sys.path.append('src')

def analyze_dataset_size():
    """分析数据集规模问题"""
    print("🔍 === 数据集规模分析 ===")
    
    try:
        from datautils import load_user_anomaly
        train_data, train_labels, test_data, test_labels = load_user_anomaly('ConstPos', load_tp=True)
        
        print(f"训练数据形状: {train_data['x'].shape}")
        print(f"测试数据形状: {test_data['x'].shape}")
        
        n_train = train_data['x'].shape[0]
        n_test = test_data['x'].shape[0]
        seq_len = train_data['x'].shape[1]
        n_features = train_data['x'].shape[2]
        
        print(f"\n📊 数据统计:")
        print(f"  训练样本数: {n_train}")
        print(f"  测试样本数: {n_test}")
        print(f"  序列长度: {seq_len}")
        print(f"  特征维度: {n_features}")
        print(f"  总数据点: {n_train * seq_len * n_features:,}")
        
        # 分析数据规模是否足够
        recommended_min_samples = 1000  # 深度学习推荐最小样本数
        recommended_batch_size = 32
        
        print(f"\n⚠️ 规模问题诊断:")
        if n_train < recommended_min_samples:
            print(f"  ❌ 训练样本过少: {n_train} < {recommended_min_samples} (推荐)")
            print(f"     影响: 模型无法学习到充分的模式，容易过拟合")
        
        if n_train < recommended_batch_size:
            print(f"  ❌ 样本数小于推荐batch_size: {n_train} < {recommended_batch_size}")
            print(f"     影响: 每个epoch只有很少的梯度更新步骤")
        
        # 计算有效的batch数量
        current_batch_size = 8
        n_batches = n_train // current_batch_size
        print(f"  📊 当前配置: batch_size={current_batch_size}, 每epoch {n_batches} 个batch")
        
        if n_batches < 10:
            print(f"  ⚠️ 每epoch的batch数过少，可能导致:")
            print(f"     - 梯度估计不稳定")
            print(f"     - 训练速度异常快")
            print(f"     - BN层统计不准确")
        
        # 分析测试集大小
        print(f"\n🔍 测试集分析:")
        print(f"  测试样本数: {n_test}")
        if n_test < 100:
            print(f"  ⚠️ 测试集过小，评估指标可能不稳定")
        
        # 检查标签分布
        print(f"\n🏷️ 标签分布分析:")
        unique_test_labels, counts = np.unique(test_labels, return_counts=True)
        print(f"  测试标签分布: {dict(zip(unique_test_labels, counts))}")
        
        if len(unique_test_labels) == 2:  # 二分类异常检测
            anomaly_ratio = counts[1] / counts.sum() if len(counts) > 1 else 0
            print(f"  异常比例: {anomaly_ratio:.3f}")
            if anomaly_ratio < 0.1 or anomaly_ratio > 0.5:
                print(f"  ⚠️ 异常比例不平衡，可能影响评估")
        
        return {
            'n_train': n_train,
            'n_test': n_test,
            'n_batches': n_batches,
            'is_sufficient': n_train >= recommended_min_samples
        }
        
    except Exception as e:
        print(f"❌ 数据集分析失败: {e}")
        return None

def analyze_model_complexity():
    """分析模型复杂度"""
    print("\n🔍 === 模型复杂度分析 ===")
    
    try:
        from timesurl import TimesURL
        
        # 创建与实际训练相同的模型配置
        args = SimpleNamespace(
            tc_timesteps=5,
            lambda_decl=0.1,
            patch_len=16,
            patch_mask_ratio=0.4
        )
        
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"使用设备: {device}")
        
        # 创建模型 (37个特征，去掉时间位置特征=36)
        model = TimesURL(
            input_dims=36,  # 排除时间位置特征
            output_dims=320,
            hidden_dims=64,
            depth=10,
            device=device,
            lr=0.0001,
            batch_size=8,
            args=args
        )
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model._net.parameters())
        trainable_params = sum(p.numel() for p in model._net.parameters() if p.requires_grad)
        
        print(f"📊 模型参数统计:")
        print(f"  总参数数: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        
        # 估算模型复杂度
        print(f"\n🔍 复杂度分析:")
        
        # 对于48个样本的数据集
        samples_per_param = 48 / trainable_params if trainable_params > 0 else 0
        print(f"  样本/参数比: {samples_per_param:.6f}")
        
        if samples_per_param < 1e-3:
            print(f"  ❌ 严重过参数化: 参数数量远超样本数")
            print(f"     模型容量过大，必然过拟合")
        elif samples_per_param < 1e-2:
            print(f"  ⚠️ 过参数化: 需要强正则化")
        
        # 测试前向传播时间
        print(f"\n⏱️ 前向传播性能测试:")
        test_batch = torch.randn(8, 500, 37).to(device)
        test_mask = torch.ones(8, 500, 36).to(device)
        
        # 预热
        with torch.no_grad():
            _ = model._net({'data': test_batch, 'mask': test_mask, 'mask_origin': test_mask})
        
        # 计时
        torch.cuda.synchronize() if device == 'cuda' else None
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(10):
                _ = model._net({'data': test_batch, 'mask': test_mask, 'mask_origin': test_mask})
        
        torch.cuda.synchronize() if device == 'cuda' else None
        avg_forward_time = (time.time() - start_time) / 10
        
        print(f"  单次前向传播时间: {avg_forward_time:.4f}s")
        print(f"  预期训练时间 (6 batches): {avg_forward_time * 6 * 2:.4f}s/epoch")  # x2 for backward
        
        return {
            'total_params': total_params,
            'samples_per_param': samples_per_param,
            'forward_time': avg_forward_time
        }
        
    except Exception as e:
        print(f"❌ 模型复杂度分析失败: {e}")
        return None

def analyze_learning_dynamics():
    """分析学习动态"""
    print("\n🔍 === 学习动态分析 ===")
    
    # 分析损失收敛模式
    print("📊 观察到的损失模式:")
    print("  初始损失: ~1.0")
    print("  最终损失: ~1.03")
    print("  改善幅度: 0.03 (3%)")
    
    print("\n🔍 损失分析:")
    print("  ⚠️ 损失改善微小，可能原因:")
    print("    1. 学习率过小 (0.0001)")
    print("    2. 数据规模不足，模型无法有效学习")
    print("    3. 损失函数权重不平衡")
    print("    4. 梯度消失或数值不稳定")
    
    # 分析DECL损失组件
    print("\n📊 DECL损失组件分析:")
    print("  重构损失权重: 1.0")
    print("  三元组损失权重: 0.1")
    print("  ⚠️ 可能问题:")
    print("    - 重构损失主导，对比学习效果弱")
    print("    - 门控网络可能选择效果不佳")
    print("    - 三元组构建质量问题")

def analyze_evaluation_issues():
    """分析评估问题"""
    print("\n🔍 === 评估问题分析 ===")
    
    print("📊 观察到的评估结果:")
    print("  F1: 0.255 (25.5%)")
    print("  Precision: 0.987 (98.7%)")
    print("  Recall: 0.147 (14.7%)")
    
    print("\n🔍 模式诊断:")
    print("  高精确度 + 低召回率 = 过度保守")
    print("  模型倾向于:")
    print("    ✓ 很少预测异常 (高精确度)")
    print("    ✗ 遗漏大量真实异常 (低召回率)")
    
    print("\n🤔 可能原因:")
    print("  1. 异常阈值设置过高")
    print("  2. 模型学习到的表征区分能力弱")
    print("  3. 训练数据中正常样本占主导地位")
    print("  4. 评估方法的归一化或阈值计算问题")
    
    print("\n💡 阈值问题分析:")
    print("  当前阈值计算: mean(train_scores) + 4*std(train_scores)")
    print("  ⚠️ 4倍标准差可能过于严格")
    print("  建议测试: 1-3倍标准差")

def provide_recommendations():
    """提供改进建议"""
    print("\n🎯 === 改进建议 ===")
    
    print("🔧 立即可行的改进:")
    print("  1. 数据增强:")
    print("     - 使用数据增强扩充训练集")
    print("     - 考虑synthetic data generation")
    print("     - 滑动窗口创建更多样本")
    
    print("\n  2. 超参数调整:")
    print("     - 提高学习率: 0.0001 → 0.001")
    print("     - 减小batch_size: 8 → 4 (更多梯度更新)")
    print("     - 增加训练epochs: 5 → 20-50")
    print("     - 调整损失权重: w_triplet 0.1 → 0.5")
    
    print("\n  3. 评估改进:")
    print("     - 测试不同阈值倍数: 1x, 2x, 3x std")
    print("     - 使用ROC曲线找最优阈值")
    print("     - 考虑相对阈值而非绝对阈值")
    
    print("\n  4. 模型简化:")
    print("     - 减小模型复杂度: depth 10 → 4-6")
    print("     - 减小隐藏维度: 64 → 32")
    print("     - 暂时禁用门控网络，测试基础DECL")
    
    print("\n🔬 深度诊断建议:")
    print("  1. 可视化分析:")
    print("     - 绘制训练损失曲线")
    print("     - 可视化学习到的表征 (t-SNE)")
    print("     - 分析异常样本特征分布")
    
    print("\n  2. 逐步验证:")
    print("     - 先用简单的reconstruction loss训练")
    print("     - 再添加对比学习")
    print("     - 最后集成门控网络")
    
    print("\n  3. 基准对比:")
    print("     - 与传统异常检测方法对比 (Isolation Forest)")
    print("     - 测试简化版TimesURL (无DECL)")
    print("     - 验证数据预处理的影响")

def main():
    """主诊断函数"""
    print("🏥 TimesURL 异常检测性能诊断")
    print("=" * 60)
    
    # 执行各项分析
    dataset_info = analyze_dataset_size()
    model_info = analyze_model_complexity()
    analyze_learning_dynamics()
    analyze_evaluation_issues()
    provide_recommendations()
    
    # 生成诊断报告
    print("\n📋 === 诊断总结 ===")
    
    if dataset_info and not dataset_info['is_sufficient']:
        print("🚨 关键问题: 数据集规模严重不足")
        print("   这是导致所有其他问题的根本原因")
    
    if model_info and model_info['samples_per_param'] < 1e-3:
        print("🚨 关键问题: 模型严重过参数化")
        print("   必须简化模型或增加数据")
    
    print("\n🎯 优先级建议:")
    print("  1. 立即: 增加学习率到0.001")
    print("  2. 立即: 调整异常阈值倍数到2-3x")
    print("  3. 短期: 数据增强或收集更多数据")
    print("  4. 短期: 简化模型架构")
    print("  5. 长期: 改进评估方法和可视化分析")

if __name__ == "__main__":
    main() 