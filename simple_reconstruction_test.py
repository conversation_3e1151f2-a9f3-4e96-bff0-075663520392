#!/usr/bin/env python3
"""
简化版重建目标对比实验
只显示关键结果，不保存详细日志
"""

import subprocess
import json
import re
from datetime import datetime

def run_single_experiment(reconstruct_target, description):
    """运行单个实验并提取关键结果"""
    
    print(f"\n🧪 运行实验: {description}")
    print(f"🎯 重建目标: {reconstruct_target}")
    
    cmd = [
        "python", "src/train.py", 
        "ConstPos", f"simple_test_{reconstruct_target}",
        "--loader", "user_anomaly", 
        "--eval",
        "--epochs", "20",  # 减少训练轮数
        "--batch-size", "16",
        "--lr", "0.001",  # 稍微大一点的学习率
        "--gpu", "0",
        "--reconstruct_target", reconstruct_target
    ]
    
    try:
        print("⏳ 训练中...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
        
        if result.returncode != 0:
            print(f"❌ 训练失败: {result.stderr}")
            return None
            
        # 提取评估结果
        output = result.stdout
        
        # 查找最后的评估结果
        eval_pattern = r"Evaluation result: \{'f1': ([0-9.]+), 'precision': ([0-9.]+), 'recall': ([0-9.]+)"
        match = re.search(eval_pattern, output)
        
        if match:
            f1 = float(match.group(1))
            precision = float(match.group(2))
            recall = float(match.group(3))
            
            print(f"✅ 训练完成")
            print(f"📈 F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
            
            return {
                'method': description,
                'target': reconstruct_target,
                'f1': f1,
                'precision': precision,
                'recall': recall
            }
        else:
            print(f"❌ 无法解析评估结果")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ 实验超时")
        return None
    except Exception as e:
        print(f"❌ 实验异常: {e}")
        return None

def main():
    """主实验流程"""
    
    print("🔬 简化版重建目标对比实验")
    print("=" * 50)
    
    experiments = [
        ("original", "重建原始信号"),
        ("denoised", "重建去噪信号")
    ]
    
    results = []
    
    for target, desc in experiments:
        result = run_single_experiment(target, desc)
        if result:
            results.append(result)
    
    # 显示结果对比
    print(f"\n{'='*50}")
    print("📊 实验结果对比")
    print(f"{'='*50}")
    
    if len(results) >= 2:
        original_result = next((r for r in results if r['target'] == 'original'), None)
        denoised_result = next((r for r in results if r['target'] == 'denoised'), None)
        
        if original_result and denoised_result:
            print(f"方案A (重建原始): F1={original_result['f1']:.4f}")
            print(f"方案B (重建去噪): F1={denoised_result['f1']:.4f}")
            
            improvement = denoised_result['f1'] - original_result['f1']
            improvement_pct = (improvement / original_result['f1']) * 100
            
            print(f"\n💡 性能差异: {improvement:+.4f} ({improvement_pct:+.1f}%)")
            
            if improvement > 0.05:
                print("✅ 结论: 重建去噪信号效果更好")
            elif improvement < -0.05:
                print("❌ 结论: 重建原始信号效果更好")
            else:
                print("🔄 结论: 两种方法效果相近")
        else:
            print("❌ 部分实验失败，无法对比")
    else:
        print("❌ 实验失败，请检查配置")
        for result in results:
            if result:
                print(f"{result['method']}: F1={result['f1']:.4f}")
    
    # 保存简化结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    simple_results = {
        'timestamp': timestamp,
        'results': results,
        'summary': f"比较了{len(results)}种重建目标方法"
    }
    
    with open(f"simple_reconstruction_results_{timestamp}.json", 'w') as f:
        json.dump(simple_results, f, indent=2)
    
    print(f"\n📁 简化结果已保存")

if __name__ == "__main__":
    main() 