当然，以下是你提供的 TimesURL 项目介绍的完整中文翻译版本：

---

# TimesURL：面向异常检测的先进时间序列表征学习框架

## 总览

**TimesURL** 是一个最先进的时间序列表征学习框架，专为**无监督异常检测**任务优化。它结合了一个强大的时间建模骨干网络（**TimesURL** 本体）与一个**解耦式对比学习模块（DECL）**，有效克服了传统异常检测方法在表征能力和鲁棒性方面的局限。

该框架尤其适用于\*\*车联网（IoV）\*\*等复杂、高维的时间序列数据场景，在这些场景中，异常通常是微妙的、依赖上下文的，并且可能隐藏在噪声中。

---

## 传统对比学习异常检测的核心问题

传统的基于对比学习的无监督异常检测通常遵循以下流程：

1. 获取一段时间序列样本。
2. 对其进行增强（如添加噪声、抖动、缩放等）。
3. 训练一个编码器，使其将原始样本与增强样本（正样本对）的表示拉近，同时与批次中其他样本的表示拉远。
4. 若一个新样本的表示与正常数据差距较大，则判为异常。

然而，这种方法存在根本缺陷：**缺乏时间上下文且对噪声敏感。**
常见的数据增强方式往往是通用的，可能会破坏定义正常行为的关键时间模式。模型学到的仅是对简单变换的“容忍性”，而非时间序列背后的**结构性规律**。

结果是，它难以区分以下两种情况：

* 无害的噪声或自然波动；
* 真正破坏时间动态结构的**结构性异常**。

---

## 我们的解决方案：TimesURL 与 DECL 的协同设计

我们提出的框架从根本上重构了这一流程，结合两个核心组件：**TimesURL 时间建模骨干网络**与**DECL 表征头模块**。

### 1. TimesURL 骨干网络：学习丰富的时间上下文

我们不再使用简单的编码器，而是引入了一个专门面向时间序列的骨干网络（基于 Transformer 与 Temporal Convolution，详见 `src/models/backbone.py` 和 `src/models/TC.py`）：

* **捕捉长期依赖关系**：显式建模跨时间跨度的数据点之间的依赖，不仅看“点”，而是理解趋势、周期和变化模式；
* **上下文感知的表示**：输出不仅是特征向量，更是包含时间动态的丰富上下文表征；
* **智能增强与去噪机制**：通过高级的增强（`src/augmentations.py`）与去噪策略（`src/denoising_utils.py`），提升信号质量而非破坏原始结构。

### 2. DECL 表征头：从表征中解耦信号与噪声

这里是关键所在。我们引入了**解耦式对比学习（Disentangled Contrastive Learning）策略**，详见 `src/models/decl_loss.py` 与 `src/models/decl_attention.py`。

* **不仅仅是“相似 vs 不同”**：DECL 模块让模型学会将稳定的、关键的“信号”与瞬态的“噪声”或风格因素分离；
* **学习稳定的本质属性**：其对比目标设计为对表面噪声不敏感，但对时间序列底层生成机制的变化极其敏感；
* **更稳健的异常评分机制**：异常不仅是“距离远”，而是对已学习到的稳定结构属性产生**扰动**。

---

### 协同优势对比

| 特性           | 传统对比异常检测方法              | **TimesURL + DECL（本框架）**                  |
| ------------ | ----------------------- | ----------------------------------------- |
| **表征方式**     | 样本级，缺乏上下文               | **上下文感知，考虑时间动态**                          |
| **对噪声的鲁棒性**  | 对噪声与浅层增强敏感              | **对噪声鲁棒，对结构变化敏感**                         |
| **检测逻辑**     | 看起来不一样（looks different） | **行为不同（behaves different）**               |
| **异常检测策略**   | 基于统计距离的离群检测             | **基于对稳定时序结构的破坏来检测异常**                     |
| **在车联网中的效果** | 高误报率，难以处理复杂攻击           | **优异的检测效果，能识别 DoS、Sybil、数据重放等微妙攻击与传感器故障** |

通过将具备时间理解能力的骨干网络（TimesURL）与可解耦噪声的 DECL 模块结合，我们的框架实现了无监督异常检测任务中更高的鲁棒性与准确率。

---

## 快速开始

1. **环境准备**：

   ```bash
   pip install -r requirements.txt
   ```

2. **准备数据集**：
   使用预设脚本准备数据集（如 IoV 场景或标准数据集）：

   ```bash
   python setup_user_dataset.py --data-name <YourDatasetName> --data-path <PathToYourCSV>
   ```

3. **训练与评估模型**：

   ```bash
   python run_user_anomaly.py --data-name <YourDatasetName> --run-name "MyFirstExperiment"
   ```

   * 更多命令行参数详见 `src/train.py` 与 `run_user_anomaly.py`，如 `--max_threads`, `--reproduce`, 以及超参设置；
   * 所有结果与模型将保存在 `training/` 目录中。

---

## 支持的数据集

框架提供了多个预处理脚本与数据加载模块，适配多种基准测试与 IoV 特定数据集，位于 `src/dataset/` 中：

* **IoV 攻击场景**：`ConstPos`, `ConstSpeed`, `DoS`, `DataReplaySybil`, `GridSybil`, `RandomPos`
* **故障场景**：`F2MD`, `Fault0.3`
* **标准基准集**：`MSL`, `SMAP`, `NAB`, `YAHOO` 等（位于 `src/dataset/benchmark/`）

---

## 项目结构

```
TimesURL/
├── DECL-main/              # 集成的 DECL 外部模块
├── src/
│   ├── dataset/            # 数据加载与预处理
│   ├── models/             # 核心模型模块（骨干、注意力、DECL）
│   ├── tasks/              # 任务逻辑模块（异常检测等）
│   ├── train.py            # 主训练与评估脚本
│   └── timesurl.py         # TimesURL 模型主体定义
├── run_user_anomaly.py     # 异常检测入口脚本
├── setup_user_dataset.py   # 自定义数据集处理脚本
└── training/               # 模型与日志输出目录
```

---

如果你需要，我还可以继续补充该文档的中文PDF版本、Overleaf格式排版或配套演示文稿。是否需要继续？
