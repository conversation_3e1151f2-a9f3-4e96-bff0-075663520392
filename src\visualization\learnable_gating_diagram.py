#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可学习门控网络(Learnable Gating Network)架构图生成脚本
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import os

def setup_chinese_font():
    """设置matplotlib中文字体支持"""
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 检查字体可用性
    try:
        import matplotlib.font_manager as fm
        chinese_fonts = [f for f in fm.fontManager.ttflist if 'SimHei' in f.name or 'Microsoft YaHei' in f.name]
        if not chinese_fonts:
            print("警告: 未找到中文字体，中文可能显示为方块")
    except Exception as e:
        print(f"字体检查失败: {e}")

def create_learnable_gating_architecture_diagram():
    """
    创建可学习门控网络架构图
    展示网络结构、去噪方法选择流程、端到端训练过程以及与启发式方法的对比
    """
    # 设置中文字体
    setup_chinese_font()
    
    # 创建画布
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    
    # 定义颜色方案
    colors = {
        'input': '#FFE4B5',        # 浅橙色 - 输入
        'gating': '#87CEEB',       # 天蓝色 - 门控网络
        'denoising': '#98FB98',    # 浅绿色 - 去噪方法
        'encoder': '#DDA0DD',      # 紫色 - 编码器
        'loss': '#F0E68C',         # 卡其色 - 损失计算
        'heuristic': '#FFA07A',    # 浅珊瑚色 - 启发式方法
        'gradient': '#FF6B6B',     # 红色 - 梯度流
        'background': '#F5F5F5'    # 浅灰色 - 背景
    }
    
    # 绘制背景区域
    # 1. 门控网络区域
    gating_bg = patches.Rectangle((0.5, 3), 6, 6, 
                                 facecolor=colors['background'], 
                                 edgecolor='gray', linewidth=1, alpha=0.3)
    ax.add_patch(gating_bg)
    ax.text(3.5, 8.7, '可学习门控网络 (Learnable Gating Network)', 
            ha='center', va='center', fontweight='bold', fontsize=14)
    
    # 2. 去噪方法区域
    denoising_bg = patches.Rectangle((7.5, 3), 4, 6, 
                                    facecolor=colors['background'], 
                                    edgecolor='gray', linewidth=1, alpha=0.3)
    ax.add_patch(denoising_bg)
    ax.text(9.5, 8.7, '去噪方法池 (Denoising Methods)', 
            ha='center', va='center', fontweight='bold', fontsize=14)
    
    # 3. 对比学习区域
    contrast_bg = patches.Rectangle((12.5, 3), 3, 6, 
                                   facecolor=colors['background'], 
                                   edgecolor='gray', linewidth=1, alpha=0.3)
    ax.add_patch(contrast_bg)
    ax.text(14, 8.7, '对比学习 (Contrastive Learning)', 
            ha='center', va='center', fontweight='bold', fontsize=14)
    
    # 4. 启发式方法区域
    heuristic_bg = patches.Rectangle((0.5, 0.5), 6, 2, 
                                    facecolor=colors['background'], 
                                    edgecolor='gray', linewidth=1, alpha=0.3)
    ax.add_patch(heuristic_bg)
    ax.text(3.5, 2.3, '启发式方法 (Heuristic Approach)', 
            ha='center', va='center', fontweight='bold', fontsize=14)
    
    # 1. 输入层
    input_box = patches.Rectangle((1, 7.5), 2, 1, 
                                 facecolor=colors['input'], 
                                 edgecolor='black', linewidth=2)
    ax.add_patch(input_box)
    ax.text(2, 8, 'Input\n[batch, seq_len, features]', 
            ha='center', va='center', fontweight='bold', fontsize=10)
    
    # 2. 门控网络结构
    # 2.1 Conv1d层
    conv_box = patches.Rectangle((1, 6), 2, 1, 
                               facecolor=colors['gating'], 
                               edgecolor='black', linewidth=1.5)
    ax.add_patch(conv_box)
    ax.text(2, 6.5, 'Conv1d\n(features→hidden_dim)', 
            ha='center', va='center', fontsize=10)
    
    # 2.2 ReLU层
    relu1_box = patches.Rectangle((1, 5), 2, 0.5, 
                                facecolor=colors['gating'], 
                                edgecolor='black', linewidth=1.5)
    ax.add_patch(relu1_box)
    ax.text(2, 5.25, 'ReLU', ha='center', va='center', fontsize=10)
    
    # 2.3 AdaptiveAvgPool1d层
    pool_box = patches.Rectangle((1, 4), 2, 0.5, 
                               facecolor=colors['gating'], 
                               edgecolor='black', linewidth=1.5)
    ax.add_patch(pool_box)
    ax.text(2, 4.25, 'AdaptiveAvgPool1d', ha='center', va='center', fontsize=10)
    
    # 2.4 Flatten层
    flatten_box = patches.Rectangle((1, 3.5), 2, 0.5, 
                                  facecolor=colors['gating'], 
                                  edgecolor='black', linewidth=1.5)
    ax.add_patch(flatten_box)
    ax.text(2, 3.75, 'Flatten', ha='center', va='center', fontsize=10)
    
    # 2.5 Linear层
    linear_box = patches.Rectangle((1, 3), 2, 0.5, 
                                 facecolor=colors['gating'], 
                                 edgecolor='black', linewidth=1.5)
    ax.add_patch(linear_box)
    ax.text(2, 3.25, 'Linear + ReLU', ha='center', va='center', fontsize=10)
    
    # 2.6 Gate层 (Softmax)
    gate_box = patches.Rectangle((4, 3.5), 2, 1, 
                               facecolor=colors['gating'], 
                               edgecolor='black', linewidth=2)
    ax.add_patch(gate_box)
    ax.text(5, 4, 'Gate (Softmax)\n[batch, num_methods]', 
            ha='center', va='center', fontweight='bold', fontsize=10)
    
    # 3. 去噪方法
    denoising_methods = [
        '移动平均\n(Moving Average)', 
        '高斯滤波\n(Gaussian Filter)', 
        'Savgol滤波\n(Savgol Filter)',
        '中值滤波\n(Median Filter)',
        '小波去噪\n(Wavelet)'
    ]
    
    for i, method in enumerate(denoising_methods):
        y_pos = 7.5 - i * 0.8
        box = patches.Rectangle((8, y_pos), 3, 0.6,
                               facecolor=colors['denoising'],
                               edgecolor='black', linewidth=1.5)
        ax.add_patch(box)
        ax.text(9.5, y_pos + 0.3, method, ha='center', va='center', fontsize=9)
        
        # 从门控到去噪方法的权重连接
        ax.arrow(6, 4, 1.8, y_pos - 3.7, head_width=0.1, head_length=0.1, 
                fc='black', ec='black', linewidth=0.5, alpha=0.7)
        
        # 权重标签
        ax.text(6.5, y_pos - 2, f'w_{i+1}', ha='center', va='center', 
                fontsize=9, fontweight='bold')
    
    # 4. 加权组合结果
    weighted_box = patches.Rectangle((8, 3), 3, 0.8,
                                   facecolor=colors['denoising'],
                                   edgecolor='black', linewidth=2)
    ax.add_patch(weighted_box)
    ax.text(9.5, 3.4, '加权组合去噪结果\n∑(w_i * method_i(x))', 
            ha='center', va='center', fontweight='bold', fontsize=10)
    
    # 5. 编码器和对比学习
    # 5.1 原始数据编码
    encoder1_box = patches.Rectangle((13, 7), 2, 0.8,
                                   facecolor=colors['encoder'],
                                   edgecolor='black', linewidth=1.5)
    ax.add_patch(encoder1_box)
    ax.text(14, 7.4, '原始数据编码\nz_orig', 
            ha='center', va='center', fontsize=10)
    
    # 5.2 去噪数据编码
    encoder2_box = patches.Rectangle((13, 5), 2, 0.8,
                                   facecolor=colors['encoder'],
                                   edgecolor='black', linewidth=1.5)
    ax.add_patch(encoder2_box)
    ax.text(14, 5.4, '去噪数据编码\nz_denoised', 
            ha='center', va='center', fontsize=10)
    
    # 5.3 对比损失
    loss_box = patches.Rectangle((13, 3), 2, 1,
                               facecolor=colors['loss'],
                               edgecolor='black', linewidth=2)
    ax.add_patch(loss_box)
    ax.text(14, 3.5, '对比损失\nL_DCL(z_denoised, z_orig)', 
            ha='center', va='center', fontweight='bold', fontsize=10)
    
    # 6. 启发式方法
    # 6.1 信号统计分析
    stats_box = patches.Rectangle((1, 1), 2, 1,
                                facecolor=colors['heuristic'],
                                edgecolor='black', linewidth=1.5)
    ax.add_patch(stats_box)
    ax.text(2, 1.5, '信号统计分析\nFFT, 自相关, 标准差', 
            ha='center', va='center', fontsize=9)
    
    # 6.2 规则选择
    rules_box = patches.Rectangle((4, 1), 2, 1,
                                facecolor=colors['heuristic'],
                                edgecolor='black', linewidth=1.5)
    ax.add_patch(rules_box)
    ax.text(5, 1.5, '硬编码规则选择\nif-then规则', 
            ha='center', va='center', fontsize=9)
    
    # 7. 连接箭头
    # 7.1 输入到Conv1d
    ax.arrow(2, 7.5, 0, -0.5, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.2 Conv1d到ReLU
    ax.arrow(2, 6, 0, -0.5, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.3 ReLU到Pool
    ax.arrow(2, 5, 0, -0.5, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.4 Pool到Flatten
    ax.arrow(2, 4, 0, -0.5, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.5 Flatten到Linear
    ax.arrow(2, 3.5, 0, -0.5, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.6 Linear到Gate
    ax.arrow(3, 3.25, 1, 0.25, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.7 去噪方法到加权组合
    for i in range(5):
        y_pos = 7.5 - i * 0.8 + 0.3
        ax.arrow(9.5, y_pos, 0, -y_pos + 3.4, head_width=0.1, head_length=0.1, 
                fc='black', ec='black', linewidth=0.5, alpha=0.7)
    
    # 7.8 输入到原始编码
    ax.arrow(3, 8, 10, -0.5, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.9 加权组合到去噪编码
    ax.arrow(11, 3.4, 2, 2, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.10 编码到损失
    ax.arrow(14, 7, 0, -1.2, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    ax.arrow(14, 5, 0, -1, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.11 启发式方法内部连接
    ax.arrow(3, 1.5, 1, 0, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1)
    
    # 7.12 启发式方法到去噪方法
    ax.arrow(6, 1.5, 2, 2, head_width=0.1, head_length=0.1, 
            fc='black', ec='black', linewidth=1, linestyle='--')
    
    # 8. 梯度反向传播
    # 8.1 损失到去噪编码
    grad1 = patches.FancyArrow(13.8, 3, 0, 1.5, width=0.05, 
                              head_width=0.2, head_length=0.2, 
                              fc=colors['gradient'], ec=colors['gradient'])
    ax.add_patch(grad1)
    
    # 8.2 去噪编码到加权组合
    grad2 = patches.FancyArrow(12.8, 5.2, -1.5, -1.5, width=0.05, 
                              head_width=0.2, head_length=0.2, 
                              fc=colors['gradient'], ec=colors['gradient'])
    ax.add_patch(grad2)
    
    # 8.3 加权组合到门控网络
    grad3 = patches.FancyArrow(7.5, 3.2, -1, 0.5, width=0.05, 
                              head_width=0.2, head_length=0.2, 
                              fc=colors['gradient'], ec=colors['gradient'])
    ax.add_patch(grad3)
    
    # 梯度标签
    ax.text(13.5, 4.2, '梯度\n反向传播', ha='center', va='center', 
            color=colors['gradient'], fontweight='bold', fontsize=9)
    
    # 9. 对比说明
    comparison_text = (
        "启发式方法 vs 可学习门控\n"
        "--------------------------------\n"
        "启发式方法：基于人工规则，与任务脱节\n"
        "可学习门控：端到端优化，直接服务于对比学习目标\n"
        "启发式方法：静态，不可学习\n"
        "可学习门控：动态适应数据特性\n"
        "启发式方法：性能天花板低\n"
        "可学习门控：持续优化，性能上限高"
    )
    
    ax.text(9.5, 1.5, comparison_text, ha='center', va='center', 
            fontsize=10, bbox=dict(boxstyle="round,pad=0.5", 
                                  facecolor='lightyellow', alpha=0.8))
    
    # 设置图形属性
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 10)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 设置标题
    title = '可学习门控网络 (Learnable Gating Network) 架构图\n端到端优化的去噪方法选择机制'
    ax.set_title(title, fontsize=18, fontweight='bold', pad=20)
    
    # 添加详细图例
    legend_elements = [
        patches.Patch(color=colors['gating'], label='门控网络模块'),
        patches.Patch(color=colors['denoising'], label='去噪方法模块'),
        patches.Patch(color=colors['encoder'], label='编码器模块'),
        patches.Patch(color=colors['loss'], label='对比损失计算'),
        patches.Patch(color=colors['heuristic'], label='启发式方法'),
        patches.Patch(color=colors['gradient'], label='梯度反向传播')
    ]
    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0, 1),
              fontsize=10, frameon=True, fancybox=True, shadow=True)
    
    plt.tight_layout()
    return fig

def main():
    """主函数：生成并显示/保存架构图"""
    print("正在生成可学习门控网络架构图...")
    
    # 创建输出目录
    output_dir = "output/diagrams"
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成架构图
    fig = create_learnable_gating_architecture_diagram()
    
    # 保存为高质量图片
    output_path = os.path.join(output_dir, "learnable_gating_architecture.png")
    fig.savefig(output_path, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print(f"架构图已保存到: {output_path}")
    
    # 同时保存为PDF格式
    pdf_path = os.path.join(output_dir, "learnable_gating_architecture.pdf")
    fig.savefig(pdf_path, format='pdf', bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"PDF版本已保存到: {pdf_path}")
    
    # 显示图片
    print("正在显示架构图...")
    plt.show()
    
    print("架构图生成完成!")

if __name__ == "__main__":
    main()