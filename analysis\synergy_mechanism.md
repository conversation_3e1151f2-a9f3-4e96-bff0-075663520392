# TimesURL两组件协同工作原理

## 1. 训练阶段协同
```python
# 对比学习：学习语义表征
loss_contrastive = hierarchical_contrastive_loss(out1, out2, ...)

# 重建学习：学习局部模式  
loss_reconstruction = mse_loss(x_recon, x_original)

# 联合优化
total_loss = λ * loss_contrastive + loss_reconstruction
```

## 2. 推理阶段协同
```python
# 步骤1：提取两种表征
repr_with_mask = model.encode(data, mask='mask_last')    # 对比学习影响
repr_without_mask = model.encode(data)                   # 原始表征

# 步骤2：计算表征差异
anomaly_score = |repr_with_mask - repr_without_mask|

# 步骤3：异常判定
is_anomaly = anomaly_score > threshold
```

## 3. 核心机制
- **掩码敏感性检测**：正常样本对掩码不敏感，异常样本敏感
- **表征一致性验证**：正常样本在不同条件下表征一致
- **多层次异常捕获**：结合局部重建和全局语义