#!/usr/bin/env python3
"""
消融实验：重建目标对比研究
========================================

研究问题：审稿人质疑 - "为何重构带噪的原始信号而不是去噪信号？"

实验设计：
1. 方案A (当前): 重建原始信号 X
2. 方案B (审稿人建议): 重建去噪信号 X_denoised  
3. 比较两种方案的异常检测性能

理论假设：
- 方案A: 学习真实数据分布，区分"正常噪声"vs"异常"
- 方案B: 学习理想干净信号，可能提供更一致的监督
"""

import subprocess
import json
import os
import re
from datetime import datetime

def run_experiment(dataset, reconstruct_target, epochs=50, description=""):
    """运行单个实验"""
    
    print(f"\n{'='*60}")
    print(f"🧪 实验: {description}")
    print(f"📊 数据集: {dataset}")
    print(f"🎯 重建目标: {reconstruct_target}")
    print(f"🔄 训练轮数: {epochs}")
    print(f"{'='*60}")
    
    # 构建命令
    cmd = [
        "python", "src/train.py", 
        dataset, f"reconstruct_{reconstruct_target}_experiment",
        "--loader", "user_anomaly", 
        "--eval",
        "--epochs", str(epochs),
        "--batch-size", "16",
        "--lr", "0.001",  # 稍微大一点的学习率避免训练问题
        "--gpu", "0",
        "--reconstruct_target", reconstruct_target,
        "--seed", "42"  # 🔧 修复：固定随机种子确保实验可重现性
    ]
    
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    # 运行实验
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print("✅ 实验完成成功")
            
            # 解析结果（寻找最终的F1分数）
            # 查找评估结果的正则表达式
            eval_pattern = r"Evaluation result: \{'f1': ([0-9.]+), 'precision': ([0-9.]+), 'recall': ([0-9.]+)"
            match = re.search(eval_pattern, result.stdout)
            
            f1_score = None
            precision = None
            recall = None
            
            if match:
                f1_score = float(match.group(1))
                precision = float(match.group(2))
                recall = float(match.group(3))
            
            # 检查训练损失函数
            print(f"🔍 检查训练损失...")
            
            # 提取每个epoch的损失
            epoch_pattern = r"Epoch #(\d+): loss=([0-9.nanf]+)"
            epoch_losses = re.findall(epoch_pattern, result.stdout)
            
            training_stable = True
            loss_info = ""
            
            if epoch_losses:
                # 检查是否有NaN损失
                nan_epochs = []
                valid_losses = []
                
                for epoch, loss_str in epoch_losses:
                    if loss_str == 'nan':
                        nan_epochs.append(int(epoch))
                        training_stable = False
                    else:
                        try:
                            valid_losses.append(float(loss_str))
                        except:
                            pass
                
                if nan_epochs:
                    loss_info = f"⚠️ 训练异常：第{nan_epochs}轮出现NaN损失"
                    print(f"⚠️ 警告：第{nan_epochs}轮训练损失为NaN，训练可能不稳定")
                else:
                    if valid_losses:
                        first_loss = valid_losses[0]
                        last_loss = valid_losses[-1]
                        improvement = first_loss - last_loss
                        loss_info = f"✅ 训练正常：{first_loss:.4f} → {last_loss:.4f} (改进{improvement:.4f})"
                        print(f"✅ 训练损失正常：{first_loss:.4f} → {last_loss:.4f}")
                    else:
                        loss_info = "❓ 无法解析损失"
                        print(f"❓ 无法解析训练损失")
            else:
                loss_info = "❓ 未找到损失信息"
                print(f"❓ 未找到训练损失信息")
            
            return {
                'reconstruct_target': reconstruct_target,
                'description': description,
                'f1_score': f1_score,
                'precision': precision,
                'recall': recall,
                'training_stable': training_stable,
                'loss_info': loss_info,
                'success': True
            }
        else:
            print(f"❌ 实验失败，返回码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
            return {
                'reconstruct_target': reconstruct_target,
                'description': description,
                'success': False,
                'error': f"训练失败，返回码: {result.returncode}"
            }
            
    except subprocess.TimeoutExpired:
        print("⏰ 实验超时")
        return {
            'reconstruct_target': reconstruct_target,
            'description': description,
            'success': False,
            'error': '实验超时'
        }
    except Exception as e:
        print(f"💥 实验异常: {e}")
        return {
            'reconstruct_target': reconstruct_target,
            'description': description,
            'success': False,
            'error': f'实验异常: {str(e)}'
        }

def main():
    """主实验流程"""
    
    print("🔬 DCRAD 重建目标消融实验")
    print("=" * 50)
    print("研究问题: 重建原始信号 vs 重建去噪信号")
    print("实验目的: 验证审稿人关于理论一致性的质疑")
    print()
    
    dataset = "ConstPos"
    epochs = 20  # 减少轮数进行快速对比
    
    experiments = [
        {
            'target': 'original',
            'description': '方案A: 重建原始信号 (当前方案)'
        },
        {
            'target': 'denoised', 
            'description': '方案B: 重建去噪信号 (审稿人建议)'
        }
    ]
    
    results = []
    
    # 运行所有实验
    for exp in experiments:
        result = run_experiment(
            dataset=dataset,
            reconstruct_target=exp['target'],
            epochs=epochs,
            description=exp['description']
        )
        results.append(result)
        
        # 简单结果汇报
        if result['success'] and result.get('f1_score') is not None:
            stability_icon = "✅" if result.get('training_stable', True) else "⚠️"
            print(f"{stability_icon} {exp['description']}: F1={result['f1_score']:.4f}, Precision={result['precision']:.4f}, Recall={result['recall']:.4f}")
            if result.get('loss_info'):
                print(f"   📊 {result['loss_info']}")
        else:
            error_msg = result.get('error', '未知错误') if result else '无返回结果'
            print(f"❌ {exp['description']}: 实验失败 - {error_msg}")
    
    # 生成结果报告
    print(f"\n{'='*60}")
    print("📊 实验结果总结")
    print(f"{'='*60}")
    
    successful_results = [r for r in results if r['success'] and r.get('f1_score') is not None]
    
    if len(successful_results) >= 2:
        original_result = next((r for r in successful_results if r['reconstruct_target'] == 'original'), None)
        denoised_result = next((r for r in successful_results if r['reconstruct_target'] == 'denoised'), None)
        
        original_f1 = original_result['f1_score'] if original_result else None
        denoised_f1 = denoised_result['f1_score'] if denoised_result else None
        
        print(f"方案A (重建原始): F1 = {original_f1:.4f}")
        print(f"方案B (重建去噪): F1 = {denoised_f1:.4f}")
        
        # 检查训练稳定性
        print(f"\n🔍 训练稳定性检查:")
        if original_result:
            stability = "✅ 稳定" if original_result.get('training_stable', True) else "❌ 不稳定"
            print(f"方案A: {stability}")
        if denoised_result:
            stability = "✅ 稳定" if denoised_result.get('training_stable', True) else "❌ 不稳定"
            print(f"方案B: {stability}")
            
            # 特别检查审稿人方案
            if not denoised_result.get('training_stable', True):
                print(f"⚠️ 警告：审稿人方案训练不稳定，结果可能不可信！")
        
        if original_f1 and denoised_f1:
            improvement = denoised_f1 - original_f1
            improvement_pct = (improvement / original_f1) * 100 if original_f1 > 0 else 0
            
            print(f"\n📈 改进效果: {improvement:+.4f} ({improvement_pct:+.1f}%)")
            
            # 结论需要考虑训练稳定性
            both_stable = (original_result.get('training_stable', True) and 
                          denoised_result.get('training_stable', True))
            
            if not both_stable:
                print("⚠️ 结论: 由于训练不稳定，结果可能不可靠，建议重新实验")
            elif improvement > 0.01:  # 显著改进
                print("✅ 结论: 审稿人的建议是正确的！重建去噪信号效果更好")
                print("💡 理论一致性确实重要：对比学习去噪 + 重建去噪 = 统一目标")
            elif improvement < -0.01:  # 显著下降  
                print("❌ 结论: 当前方案更好，重建原始信号有其合理性")
                print("💡 可能原因: 需要学习真实数据分布，包括正常变异")
            else:
                print("🔄 结论: 两种方案效果相近，需要更深入研究")
    else:
        print("❌ 实验不足，无法得出结论")
    
    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"reconstruction_target_experiment_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 详细结果已保存到: {results_file}")
    
    return results

if __name__ == "__main__":
    main() 