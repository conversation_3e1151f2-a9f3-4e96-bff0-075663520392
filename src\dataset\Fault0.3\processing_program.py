import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

# 读取CSV文件，明确指定有header
df = pd.read_csv('Atkallfault0.3.csv', header=0) # header=0 表示第一行是表头

# 获取最后一列的列名
label_col_name = df.columns[-1]

# 将最后一列命名为 "Label" (如果需要，虽然header=0已经读取了列名)
# 如果最后一列名不是'Label'，可以取消下面这行的注释
# df.rename(columns={label_col_name: 'Label'}, inplace=True)

print(f"原始数据形状: {df.shape}")
print(f"列名: {df.columns.tolist()}") # 打印列名以供检查
print(f"标签类型统计:\n{df[label_col_name].value_counts()}")

# 保留所有数据，不做特殊筛选
# 但可以查看各种异常类型的分布
anomaly_samples = df[df[label_col_name] != 'Genuine']
genuine_samples = df[df[label_col_name] == 'Genuine']
print(f"正常样本数量: {len(genuine_samples)}")
print(f"异常样本数量: {len(anomaly_samples)}")
print(f"异常类型分布:\n{anomaly_samples[label_col_name].value_counts()}")

# 划分训练集和测试集 (80%训练, 20%测试)
total_samples = len(df)
train_size = int(total_samples * 0.7)

# 随机打乱数据
# df = df.sample(frac=1, random_state=42)

train_df = df.iloc[:train_size]
test_df = df.iloc[train_size:]

print(f"训练集大小: {train_df.shape}")
print(f"测试集大小: {test_df.shape}")

# 分离特征和标签
# 特征是从第5列到第19列 (共15列)
# 注意：因为有了header，iloc仍然是基于0的整数索引，所以5:20是正确的
train_features = train_df.iloc[:, 3:43].values
test_features = test_df.iloc[:, 3:43].values

# 标准化特征
# scaler = StandardScaler()
# train_features = scaler.fit_transform(train_features)
# test_features = scaler.transform(test_features)

# 分离标签 (使用最后一列的列名)
test_labels = test_df[label_col_name].map(lambda x: 0 if x == "Genuine" else 1).values

print(f"训练特征形状: {train_features.shape}")
print(f"测试特征形状: {test_features.shape}")
print(f"测试标签形状: {test_labels.shape}")
print(f"标签分布: 正常样本: {np.sum(test_labels == 0)}, 异常样本: {np.sum(test_labels == 1)}")

# 保存到npy文件
np.save('Fault0.3_train.npy', train_features)
np.save('Fault0.3_test.npy', test_features)
np.save('Fault0.3_test_label.npy', test_labels)

print("已生成以下文件:")
print("Fault0.3_train.npy - 训练集特征")
print("Fault0.3_test.npy - 测试集特征")
print("Fault0.3_test_label.npy - 测试集标签")



